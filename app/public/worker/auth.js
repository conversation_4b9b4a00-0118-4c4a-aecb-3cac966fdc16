let interval
let isStart = false
let i = 0
self.onmessage = (e) => {
	const message = e.data

	if (message === 'start' && !isStart) {
		isStart = true
		console.warn('worker| start interval')
		interval = setInterval(() => {
			i += 1
			if (i > 60) {
				self.postMessage('checked')
				i = 0
			}
		}, 1000)
		if (interval) {
			self.postMessage('interval-start')
			self.postMessage('checked')
		}
	}
	if (message === 'stop' && isStart) {
		isStart = false
		clearInterval(interval)
		self.postMessage('interval-clear')
		console.warn('worker| stop interval')
	}
	if (message !== 'start' && message !== 'stop') {
		self.postMessage('other: ', message)
	}
}

/*
self.addEventListener('push', function (event) {
	if (!(self.Notification && self.notification.permission === 'granted')) {
		return
	}

	let data = {}
	if (event.data) {
		data = event.data.json()
	}
	const title = data.title || 'Something Has Happened'
	const message = data.message || "Here's something you might want to check out."
	const icon = 'images/new-notification.png'

	const notification = new Notification(title, {
		body: message,
		tag: 'simple-push-demo-notification',
		icon
	})

	notification.addEventListener('click', function () {
		if (clients.openWindow) {
			clients.openWindow('https://example.blog.com/2015/03/04/something-new.html')
		}
	})
})
*/
