const path = require('path')
const fs = require('fs')
const { execSync } = require('child_process')

const pathCellEntitiesIn = path.join(process.cwd(), './api_src/cell')
const pathFiberEntitiesIn = path.join(process.cwd(), './api_src/fiber')
const pathCellEntitiesOut = path.join(process.cwd(), './src/entities/cell')
const pathFiberEntitiesOut = path.join(process.cwd(), './src/entities/fiber')

let filesCell = fs.readdirSync(pathCellEntitiesIn)
let filesFiber = fs.readdirSync(pathFiberEntitiesIn)

macro(filesCell, pathCellEntitiesIn, pathCellEntitiesOut, 'Cell')
macro(filesFiber, pathFiberEntitiesIn, pathFiberEntitiesOut, 'Fiber')

function macro(files, pathEntitiesIn, pathEntitiesOut, type) {
	if (files.length > 0) {
		files.forEach((file) => {
			renamFile(file, pathEntitiesIn, type)
		})
	}
	execSync('pnpm prettier --write ' + pathEntitiesIn)
	files = fs.readdirSync(pathEntitiesIn)

	if (files.length > 0) {
		files.forEach((file) => {
			checkProperties(file, pathEntitiesIn, pathEntitiesOut)
		})
	}
}

function checkProperties(file, pathEntitiesIn, pathEntitiesOut) {
	const filePathIn = path.join(pathEntitiesIn, file)
	const filePathOut = path.join(pathEntitiesOut, file)
	const fileReadIn = fs.readFileSync(filePathIn, 'utf-8')
	const fileReadSplitIn = fileReadIn.trim().split(/\r?\n/)

	if (fs.existsSync(filePathOut)) {
		const fileReadOut = fs.readFileSync(filePathOut, 'utf-8')
		const fileReadSplitOutAll = fileReadOut.trim().split(/\n\n/)
		const fileReadSplitOut = fileReadSplitOutAll[0].split(/\r?\n/)

		let lineFileInNotInLineFileOut = []

		fileReadSplitIn.forEach((lineFileIn, indexFileIn) => {
			if (indexFileIn !== 0) {
				const lineFileSplitIn = lineFileIn.trim().split(/(:|\?:)/)

				let isLineFileInInLineFileOut = false

				fileReadSplitOut.forEach((lineFileOut, indexFileOut) => {
					const lineFileSplitOut = lineFileOut.trim().split(/(:|\?:)/)

					if (indexFileOut !== 0) {
						if (lineFileSplitIn[0] === lineFileSplitOut[0]) {
							isLineFileInInLineFileOut = true
						}
					}
				})
				if (!isLineFileInInLineFileOut) {
					lineFileInNotInLineFileOut.push({ data: lineFileIn, line: indexFileIn })
				}
			}
		})
		if (lineFileInNotInLineFileOut.length > 0) {
			console.log(lineFileInNotInLineFileOut, file)

			lineFileInNotInLineFileOut.forEach((line) => {
				fileReadSplitOut.splice(line.line, 0, line.data)
			})

			fileReadSplitOutAll[0] = fileReadSplitOut.join(`\n`)

			fs.writeFileSync(filePathOut, fileReadSplitOutAll.join(`\n\n`))
		}
	} else {
		fs.writeFileSync(filePathOut, fileReadIn)
	}
}
function renamFile(file, pathEntitiesIn, type) {
	const pathFile = path.join(pathEntitiesIn, file)
	const dataFile = fs.readFileSync(pathFile, { encoding: 'utf8' })
	const regex = new RegExp(`interface I${type}.* {`)

	if (!dataFile.match(regex)) {
		const dataMatchFile = dataFile.match('interface .* {')[0]
		const newNameFile = dataMatchFile.slice(10, dataMatchFile.length - 2)
		const pathNewFile = path.join(pathEntitiesIn, `${newNameFile}.interface.ts`)

		fs.renameSync(pathFile, pathNewFile)
		let newDataFile = dataFile.replace(/interface .* {/, `interface I${type}${newNameFile} {`)
		fs.writeFileSync(pathNewFile, newDataFile)
	}
}
