const { execSync } = require('child_process')

try {
	execSync('pnpm init:npm-config', { encoding: 'utf-8', stdio: 'pipe' })
} catch (e) {
	console.log(e)
	process.exit(1)
}

const Listr = require('listr')
const shell = require('shelljs')

const tasks = new Listr([
	{
		title: 'Certs',
		task: () => {
			if (shell.exec('pnpm init:certs', { silent: true }).code !== 0) {
				return Promise.reject(new Error('Certs'))
			} else {
				return Promise.resolve()
			}
		}
	},
	{
		title: 'husky',
		task: () => {
			if (shell.exec('pnpm init:husky', { silent: true }).code !== 0) {
				return Promise.reject(new Error('husky'))
			} else {
				return Promise.resolve()
			}
		}
	}
])

tasks.run().catch((err) => {
	console.error(err)
})
