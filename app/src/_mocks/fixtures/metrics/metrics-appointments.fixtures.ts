// utils
import { eachDayOfInterval, format } from 'date-fns';

// ----------------------------------------------------------------------

export const metricsAppointments = (start: Date, end: Date) => {
	const daysInterval = eachDayOfInterval({ start, end });

	return daysInterval.reduce((prev, curr) => {
		const formattedDate = format(curr, 'yyyy-MM-dd');

		return {
			...prev,
			[formattedDate]: {
				outsideBusinessHours: {
					offers: {
						'Offre fibre avec Freebox Pro': Math.round(10 * Math.random()),
						'Offre FTTH activée lien nu': Math.round(10 * Math.random())
					},
					speeds: {
						fast: Math.round(10 * Math.random()),
						normal: Math.round(10 * Math.random()),
						slow: Math.round(10 * Math.random())
					},
					states: {
						ko: Math.round(10 * Math.random()),
						ok: Math.round(10 * Math.random()),
						wait: Math.round(10 * Math.random())
					},
					technologies: {
						PMGC: Math.round(10 * Math.random()),
						PON_CAD: Math.round(10 * Math.random()),
						PON_LNP: Math.round(10 * Math.random()),
						PON_P2P: Math.round(10 * Math.random())
					},
					types: {
						analyse: Math.round(10 * Math.random()),
						devis: Math.round(10 * Math.random()),
						post_production: Math.round(10 * Math.random()),
						previsite: Math.round(10 * Math.random()),
						raccordement: Math.round(10 * Math.random()),
						sans_contact: Math.round(10 * Math.random()),
						support: Math.round(10 * Math.random()),
						travaux: Math.round(10 * Math.random())
					},
					uprs: {
						'00': Math.round(10 * Math.random()),
						'01': Math.round(10 * Math.random()),
						'02': Math.round(10 * Math.random()),
						'03': Math.round(10 * Math.random()),
						'04': Math.round(10 * Math.random()),
						'05': Math.round(10 * Math.random()),
						'06': Math.round(10 * Math.random()),
						'07': Math.round(10 * Math.random()),
						'08': Math.round(10 * Math.random()),
						'09': Math.round(10 * Math.random()),
						10: Math.round(10 * Math.random()),
						11: Math.round(10 * Math.random()),
						12: Math.round(10 * Math.random()),
						13: Math.round(10 * Math.random()),
						14: Math.round(10 * Math.random()),
						15: Math.round(10 * Math.random()),
						16: Math.round(10 * Math.random()),
						17: Math.round(10 * Math.random()),
						18: Math.round(10 * Math.random())
					}
				},
				withinBusinessHours: {
					offers: {
						'Offre fibre avec Freebox Pro': Math.round(10 * Math.random()),
						'Offre FTTH activée lien nu': Math.round(10 * Math.random())
					},
					speeds: {
						fast: Math.round(10 * Math.random()),
						normal: Math.round(10 * Math.random()),
						slow: Math.round(10 * Math.random())
					},
					states: {
						ko: Math.round(10 * Math.random()),
						ok: Math.round(10 * Math.random()),
						wait: Math.round(10 * Math.random())
					},
					technologies: {
						PMGC: Math.round(10 * Math.random()),
						PON_CAD: Math.round(10 * Math.random()),
						PON_LNP: Math.round(10 * Math.random()),
						PON_P2P: Math.round(10 * Math.random())
					},
					types: {
						analyse: Math.round(10 * Math.random()),
						devis: Math.round(10 * Math.random()),
						post_production: Math.round(10 * Math.random()),
						previsite: Math.round(10 * Math.random()),
						raccordement: Math.round(10 * Math.random()),
						sans_contact: Math.round(10 * Math.random()),
						support: Math.round(10 * Math.random()),
						travaux: Math.round(10 * Math.random())
					},
					uprs: {
						'00': Math.round(10 * Math.random()),
						'01': Math.round(10 * Math.random()),
						'02': Math.round(10 * Math.random()),
						'03': Math.round(10 * Math.random()),
						'04': Math.round(10 * Math.random()),
						'05': Math.round(10 * Math.random()),
						'06': Math.round(10 * Math.random()),
						'07': Math.round(10 * Math.random()),
						'08': Math.round(10 * Math.random()),
						'09': Math.round(10 * Math.random()),
						10: Math.round(10 * Math.random()),
						11: Math.round(10 * Math.random()),
						12: Math.round(10 * Math.random()),
						13: Math.round(10 * Math.random()),
						14: Math.round(10 * Math.random()),
						15: Math.round(10 * Math.random()),
						16: Math.round(10 * Math.random()),
						17: Math.round(10 * Math.random()),
						18: Math.round(10 * Math.random())
					}
				}
			}
		};
	}, {});
};
