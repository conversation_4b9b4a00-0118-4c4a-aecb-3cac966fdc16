// utils
import { http, HttpResponse } from 'msw';
// others
import { listSerializer } from '@/mocks/ctx/list-response';
import { telemetries } from '@/mocks/fixtures/telemetries.fixtures';

// ----------------------------------------------------------------------

export const getOrderTelemetriesHandler = http.get('*/api/fiber/orders/:id/telemetries', (_, res) => {
	return res(listSerializer(telemetries));
});
