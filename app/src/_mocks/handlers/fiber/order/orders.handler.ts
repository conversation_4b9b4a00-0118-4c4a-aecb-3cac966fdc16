// utils
import { http, HttpResponse } from 'msw';
// others
import { listSerializer } from '@/mocks/ctx/list-response';
import { orderPlanning } from '@/mocks/fixtures/fiber/order';
import { getOrder, ordersFixture } from '@/mocks/fixtures/fiber/order/order.fixtures';

// ----------------------------------------------------------------------

export const getOrderHandler = http.get('*/api/fiber/orders/:id', (req, res, ctx) => {
	return res(HttpResponse.jsongetOrder(`/orders/${req.params.id}`));
	)
});
export const getOrdersHandler = http.get('*/api/fiber/orders', (req, res) => {
	let ordersRep = ordersFixture;

	const params = {
		reference: req.url.searchParams.get('reference')
	};

	if (params.reference) {
		const regex = new RegExp(`${params.reference}`, 'i');
		ordersRep = ordersFixture.filter((order) => {
			return regex.test(order.reference);
		});
	}

	return res(listSerializer(ordersRep));
});
export const getOrderPlanningHandler = http.get('*/api/fiber/orders/:id/planning', (req, res, ctx) => {
	const urlParams = new URLSearchParams(req.url.search);
	const from: string = urlParams.get('from') || 'now';
	const to: string = urlParams.get('to') || 'now';

	return res(HttpResponse.jsonorderPlanning(from, to));
	)
});
