// utils
import { http, HttpResponse } from 'msw';
// others
import { listSerializer } from '@/mocks/ctx/list-response';
import { categoriesFixture } from '@/mocks/fixtures/magnet';

// ----------------------------------------------------------------------

export const getCategoriesHandler = http.get('*/magnet/categories', (_req, _res) => {
	return _res(listSerializer(categoriesFixture));
});
export const getCategoriesDetailsHandler = http.get('*/magnet/categories/:id/details', (_req, _res, ctx) => {
	return _res(
		HttpResponse.json{
			name: 'SAV',
			categories: [],
			subjects: [
				{
					'@id': '/subjects/23',
					id: 23,
					name: 'État des services',
					details: [
						{
							'@id': '/subject_details/29',
							id: 29,
							name: 'reference',
							type: 'string',
							label: 'Jeton de commande'
						},
						{
							'@id': '/subject_details/30',
							id: 30,
							name: 'step_box_before',
							type: 'string',
							label: 'Étape avant redémarrage'
						},
						{
							'@id': '/subject_details/31',
							id: 31,
							name: 'is_restart_box',
							type: 'boolean',
							label: 'Redémarrage BOX'
						},
						{
							'@id': '/subject_details/32',
							id: 32,
							name: 'step_box_after',
							type: 'string',
							label: 'Étape après redémarrage'
						},
						{
							'@id': '/subject_details/33',
							id: 33,
							name: 'is_restart_onu',
							type: 'boolean',
							label: 'Redémarrage ONU'
						},
						{
							'@id': '/subject_details/34',
							id: 34,
							name: 'state_fiber_before',
							type: 'string',
							label: 'État de la fibre avant redémarrage'
						},
						{
							'@id': '/subject_details/35',
							id: 35,
							name: 'state_power_before',
							type: 'string',
							label: "État de l'alimentation avant redémarrage"
						},
						{
							'@id': '/subject_details/36',
							id: 36,
							name: 'state_box_before',
							type: 'string',
							label: 'État de la box avant redémarrage'
						},
						{
							'@id': '/subject_details/37',
							id: 37,
							name: 'state_fiber_after',
							type: 'string',
							label: 'État de la fibre après redémarrage'
						},
						{
							'@id': '/subject_details/38',
							id: 38,
							name: 'state_power_after',
							type: 'string',
							label: "État de l'alimentation après redémarrage"
						},
						{
							'@id': '/subject_details/39',
							id: 39,
							name: 'state_box_after',
							type: 'string',
							label: 'État de la box après le redémarrage'
						}
					]
				}
			]
		})
	)
});

const categoryHandler = [getCategoriesHandler, getCategoriesDetailsHandler];

export default categoryHandler;
