// utils
import { http, HttpResponse } from 'msw';

// ----------------------------------------------------------------------

export const getSubjectsHandler = http.get('*/magnet/subjects/:id', (_req, _res, ctx) => {
	return _res(
		HttpResponse.json{
			'@context': '/contexts/Subject',
			'@id': '/subjects/23',
			'@type': 'Subject',
			body: "Bonjour,\n\nJeton de commande : {{reference}}.\n\nLes informations sur la box :\n\n- Étape avant redémarrage : {{step_box_before}}\n- Redémarrage effectué : {{is_restart_box}}\n- Étape après redémarrage : {{step_box_after}}\n\nLes informations sur l'ONU :\n\n- Redémarrage effectué : {{is_restart_onu}}\n- État de la fibre avant redémarrage : {{state_fiber_before}}\n- État de l'alimentation avant redémarrage : {{state_power_before}}\n- État de la box avant redémarrage : {{state_box_before}}\n- État de la fibre après redémarrage : {{state_fiber_after}}\n- État de l'alimentation après redémarrage : {{state_power_after}}\n- État de la box après le redémarrage : {{state_box_after}}\n\nMerci.",
			arborescence: '/arborescences/9',
			name: 'État des services',
			subjectDetails: [
				{
					'@id': '/subject_details/29',
					'@type': 'SubjectDetail',
					name: 'reference',
					type: 'string',
					label: 'Jeton de commande'
				},
				{
					'@id': '/subject_details/30',
					'@type': 'SubjectDetail',
					name: 'step_box_before',
					type: 'string',
					label: 'Étape avant redémarrage'
				},
				{
					'@id': '/subject_details/31',
					'@type': 'SubjectDetail',
					name: 'is_restart_box',
					type: 'boolean',
					label: 'Redémarrage BOX'
				},
				{
					'@id': '/subject_details/32',
					'@type': 'SubjectDetail',
					name: 'step_box_after',
					type: 'string',
					label: 'Étape après redémarrage'
				},
				{
					'@id': '/subject_details/33',
					'@type': 'SubjectDetail',
					name: 'is_restart_onu',
					type: 'string',
					label: 'Redémarrage ONU'
				},
				{
					'@id': '/subject_details/34',
					'@type': 'SubjectDetail',
					name: 'state_fiber_before',
					type: 'string',
					label: 'État de la fibre avant redémarrage'
				},
				{
					'@id': '/subject_details/35',
					'@type': 'SubjectDetail',
					name: 'state_power_before',
					type: 'string',
					label: "État de l'alimentation avant redémarrage"
				},
				{
					'@id': '/subject_details/36',
					'@type': 'SubjectDetail',
					name: 'state_box_before',
					type: 'string',
					label: 'État de la box avant redémarrage'
				},
				{
					'@id': '/subject_details/37',
					'@type': 'SubjectDetail',
					name: 'state_fiber_after',
					type: 'string',
					label: 'État de la fibre après redémarrage'
				},
				{
					'@id': '/subject_details/38',
					'@type': 'SubjectDetail',
					name: 'state_power_after',
					type: 'string',
					label: "État de l'alimentation après redémarrage"
				},
				{
					'@id': '/subject_details/39',
					'@type': 'SubjectDetail',
					name: 'state_box_after',
					type: 'string',
					label: 'État de la box après le redémarrage'
				}
			]
		})
	)
});
