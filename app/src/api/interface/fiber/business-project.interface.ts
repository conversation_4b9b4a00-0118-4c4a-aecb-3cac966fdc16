// api
import type { TFiberContact } from '@/api/interface/fiber';
import type { IOrderItem } from '@/api/interface/fiber';

// ----------------------------------------------------------------------

export interface IFiberBusinessProject {
	'@id'?: string;
	isValidated: boolean;
	retailerReference: string;
	customerReference?: string;
	comment?: string;
	caReference?: string;
	orders: IOrderItem[];
	targetOrderCount: number;
	deploymentType: TFiberBusinessProjectDeploymentType;
	state: TFiberBusinessProjectState;
	expectedAt: Date;
	businessProjectContacts: TFiberContact[];
	readonly company?: string;
	readonly createdAt?: Date;
	readonly updatedAt?: Date;
	readonly deletedAt?: Date;
}

export type TFiberBusinessProjectDeploymentType = 'any' | 'synchronized';
export type TFiberBusinessProjectState = keyof typeof EFiberBusinessProjectState;
export enum EFiberBusinessProjectState {
	CREATED = 'en projet',
	VALIDATED = 'validé',
	STARTED = 'en contruction',
	DONE = 'terminé',
	CANCELLED = 'annulé'
}
