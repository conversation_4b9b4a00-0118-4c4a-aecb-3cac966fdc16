interface IAddress {
	street: string;
	street2?: string;
	postalCode: string;
	city: string;
	country: 'France' | 'England' | 'Italia' | 'Switzerland';
	iwId?: number;
	keyInterOp?: string;
	banId?: string;
	geoPointLat?: number;
	geoPointLon?: number;
	hexacle?: string;
	latitude?: string;
	longitude?: string;
}

export interface IFiberEndpointAddress extends IAddress {
	'@id'?: string;
	'@type'?: string;
}
