// others
import type { IFiberItemNetwork } from './item-network.interface';
import type { IFiberPtoOutputs } from './pto-outputs.interface';

// ----------------------------------------------------------------------

export type TFiberColor =
	| 'red'
	| 'blue'
	| 'green'
	| 'yellow'
	| 'brown'
	| 'turquoise'
	| 'orange'
	| 'black'
	| 'grey'
	| 'pink'
	| 'purple'
	| 'white'
	| 'lime';

export interface IColors {
	'@id'?: string;
	'@type': string;
	'@context'?: string;
	module: number;
	tube: TFiberColor;
	fiber: TFiberColor;
}

export interface Nro {
	'@type': string;
	addressIwId: string;
	baie: string;
	baieId: number;
	id: number;
	name: string;
}

export interface Pm {
	'@type': string;
	name: string;
	iwId?: number;
	colors: IColors;
	technicalName: string;
	referencePrestation: string;
	type: TPMType;
}
export type TPMType = keyof typeof EPMType | 'undefined';
export enum EPMType {
	pm_interieur = 'PMI',
	pm_exterieur = 'PME',
	pm_rue = 'PMR',
	local_technique = 'LOCAL'
}
export interface Pto {
	'@type': string;
	pmt: string;
	referencePrestation: string;
	type: string;
	name: string;
	colors: IColors;
}

export interface Passif {
	'@type': string;
	column: string;
	drawer: number;
	drawerIwId: number;
	line: number;
	positionIwId: number;
}
export interface CouplerPm {
	'@type': string;
	name: string;
	position: number;
	size: number;
}

export interface IFiberROP {
	'@id'?: string;
	'@type'?: string;
	reference: string;
	nro: Nro;
	pm: Pm;
	pto: Pto;
	ptoComponents: IFiberPtoOutputs[];
	nroPassive: Passif;
	distance?: number;
	oi?: string;
	networkComponents?: IFiberItemNetwork[];
	isMigration: boolean;
	ptoOutputs: any;
	items: any;
	readonly createdAt?: Date;
	readonly updatedAt?: Date;
	readonly deletedAt?: Date;
}
