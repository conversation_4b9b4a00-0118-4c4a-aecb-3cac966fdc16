// api
import type { HydraMember, HydraResponse } from '@/api/types';

// ----------------------------------------------------------------------

export type ISpace = HydraResponse<ISpaceItem>;

export interface ISpaceItem extends HydraMember {
	name: string;
	outsources: any[];
	spaceTeams: SpaceTeam[];
}

// FORMS
export interface ISpaceForm {
	name: string;
	teams?: string[];
}

export interface SpaceTeam {
	'@id': string;
	'@type': string;
	team: string;
}
