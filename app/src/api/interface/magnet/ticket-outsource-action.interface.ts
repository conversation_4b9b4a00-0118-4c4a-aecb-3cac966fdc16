// api
import type { HydraMember, HydraResponse } from '@/api/types';

// ----------------------------------------------------------------------

export type ITicketOutsourceActions = HydraResponse<ITicketOutsourceAction>;

export interface ITicketOutsourceAction extends HydraMember {
	outsourceAction: IOutsourceAction;
	createdAt: string | Date;
	user: ITicketOutsourceActionUser;
}

export interface IOutsourceAction extends HydraMember {
	action: string;
}

export interface ITicketOutsourceActionUser extends HydraMember {
	iri: string;
	team: string;
	domain: string;
}

// FORMS
// ----------------------------------------------------------------------

export interface ITicketOutsourceActionForm {
	outsourceAction: string;
}
