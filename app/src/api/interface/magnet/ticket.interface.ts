// api
import type { TTicketCloseEnum, TTicketPriorityEnum, TTicketStateEnum } from '@/api/enums';
import type { TFormFieldTypeFrontEnum } from '@/api/enums/magnet/form-field-type-front.enum.ts';
import type { TFormFieldTypeEnum } from '@/api/enums/magnet/form-field-type.enum.ts';
import type { HydraMember, HydraResponse } from '@/api/types';

// ----------------------------------------------------------------------

export type ITicket = HydraResponse<ITicketBase>;

export interface ITicketBase extends HydraMember {
	reference: string;
	comment: string;
	closeComment?: TTicketCloseEnum;
	state: TTicketStateEnum;
	cc: string[];
	priority: TTicketPriorityEnum;
	openedAt: string | Date;
	urlImg?: string;
	assignedTo?: string;
	activeOutsources?: string[];
	ticketOutsources?: { state: string; externalId: string; context: { gtaId: number } }[];
	deletedAt?: string | Date;
	space: string;
	form: {
		name: string;
	};
	messages: ITicketMessage[];
	formValues: ITicketFormValue[];
	createdAt: string | Date;
	updatedAt: string | Date;
	user: ITicketUser;
}

export interface ITicketMessage extends HydraMember {
	content: string;
	createdAt: string | Date;
	user: ITicketUser;
	outsources: string[];
	documents: string[];
}

export interface ITicketUser extends HydraMember {
	iri: string;
	team: string;
	domain: string;
}

export interface ITicketFormValue extends HydraMember {
	value: string;
	formField: ITicketFormField;
	documents?: string[];
}

export interface ITicketFormField extends HydraMember {
	name: string;
	type: TFormFieldTypeEnum;
	inputType: TFormFieldTypeFrontEnum;
	label: string;
	validation?: string;
	enum?: string[];
	required: boolean;
}

// FORMS
// ----------------------------------------------------------------------

export interface ITicketForm {
	reference?: string;
	comment: string;
	cc?: string[];
	priority: TTicketPriorityEnum;
	space: string;
	form: string;
	formValues: ITicketFormValueInput[];
}

export interface ITicketFormValueInput {
	value: string;
	formField: string;
	documents?: string[];
}

export interface ITicketMessageForm {
	content: string;
}
