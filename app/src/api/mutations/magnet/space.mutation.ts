// tanstack
import { useMutation } from '@tanstack/react-query';
// utils
import { API_ENDPOINTS_PAWN } from '@/utils/axios';
// api
import type { ISpaceForm, ISpaceItem } from '@/api/interface/magnet';
import type { IApiPlatformError } from '@/api/types';
import { magnetFetcher } from '@/api/fetcher';
import { magnetQueryKeys } from '@/api/queries';
// services
import { queryClient } from '@/services/query-client.service';

// ----------------------------------------------------------------------

export function useCreateSpace() {
	const URL = API_ENDPOINTS_PAWN.spaces;

	return useMutation<ISpaceItem, IApiPlatformError, ISpaceForm>({
		mutationFn: (data) => magnetFetcher({ url: URL, method: 'post', data }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: magnetQueryKeys.magnet_space.all._def });
		}
	});
}

export function useUpdateSpace({ spaceIri }: { spaceIri: string }) {
	return useMutation<ISpaceItem, IApiPlatformError, ISpaceForm>({
		mutationFn: (data) =>
			magnetFetcher({
				url: spaceIri,
				method: 'patch',
				data,
				config: {
					headers: {
						'Content-Type': 'application/merge-patch+json'
					}
				}
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: magnetQueryKeys.magnet_space.all._def });
		}
	});
}

export function useDeleteSpace() {
	return useMutation<ISpaceItem, Error, string>({
		mutationFn: (spaceIri) => magnetFetcher({ url: spaceIri, method: 'delete' }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: magnetQueryKeys.magnet_space.all._def });
		}
	});
}
