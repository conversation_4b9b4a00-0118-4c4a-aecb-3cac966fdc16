// tanstack
import { useMutation } from '@tanstack/react-query';
// utils
import { API_ENDPOINTS_PAWN } from '@/utils/axios';
// api
import type { ITeamForm, ITeamItem } from '@/api/interface/pawn';
import type { IApiPlatformError } from '@/api/types';
import { pawnFetcher } from '@/api/fetcher';
import { pawnQueryKeys } from '@/api/queries';
// services
import { queryClient } from '@/services/query-client.service';

// ----------------------------------------------------------------------

export function useCreateTeam() {
	const URL = API_ENDPOINTS_PAWN.teams;

	return useMutation<ITeamItem, IApiPlatformError, ITeamForm>({
		mutationFn: (data) => pawnFetcher({ url: URL, method: 'post', data }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_team._def });
		}
	});
}

export function useUpdateTeam({ teamIri }: { teamIri: string }) {
	return useMutation<ITeamItem, IApiPlatformError, ITeamForm>({
		mutationFn: (data) =>
			pawnFetcher({
				url: teamIri,
				method: 'patch',
				data,
				config: {
					headers: {
						'Content-Type': 'application/merge-patch+json'
					}
				}
			}),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_team._def });
		}
	});
}

export function useDeleteTeam() {
	return useMutation<ITeamItem, Error, string>({
		mutationFn: (teamIri) => pawnFetcher({ url: teamIri, method: 'delete' }),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: pawnQueryKeys.pawn_team._def });
		}
	});
}
