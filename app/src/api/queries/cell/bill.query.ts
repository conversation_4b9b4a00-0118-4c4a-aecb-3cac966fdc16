// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
// api
import type { ICellBill } from '@/api/interface/cell';
import { cellFetcher, parseIri } from '@/api/fetcher';

// ----------------------------------------------------------------------

export const cellBillQueryKeys = createQueryKeys('cell_bill', {
	byId: (id?: string) => {
		const billId = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [billId],
			queryFn: () => cellFetcher<ICellBill>({ url: billId })
		};
	}
});
