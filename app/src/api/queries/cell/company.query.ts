// utils
import { createQuery<PERSON>eys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { ICellCompanyContact } from '@/api/interface/cell';
import type { ICellCompanyDevice } from '@/api/interface/cell';
import type { ICellCompanySimCard } from '@/api/interface/cell';
import type { ICellCompany } from '@/api/interface/cell';
import type { ICellDeliveryAddress } from '@/api/interface/cell';
import type { ICellFleetManager } from '@/api/interface/cell';
import type { ICellFleet } from '@/api/interface/cell';
import type { ICellLegalPerson } from '@/api/interface/cell';
import { cellFetcher } from '@/api/fetcher';
import type { TCellDeviceParams, TCellFleetParam, TCellSimCardParams } from '@/types/params';
import type { HydraResponse } from '@/types';

// ----------------------------------------------------------------------

export const cellCompanyQueryKeys = createQueryKeys('cell_company', {
	all: () => ({
		queryKey: [''],
		queryFn: () => cellFetcher<HydraResponse<ICellCompany>>({ url: 'companies' })
	}),
	byId: (id: string | undefined) => {
		const companyId: string = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [companyId],
			queryFn: () => cellFetcher<ICellCompany>({ url: `companies/${companyId}` }),
			contextQueries: {
				fleets: {
					queryKey: null,
					queryFn: () => cellFetcher<HydraResponse<ICellFleet>>({ url: `companies/${companyId}/fleets` })
				},
				contacts: {
					queryKey: null,
					queryFn: () => cellFetcher<HydraResponse<ICellCompanyContact>>({ url: `companies/${companyId}/contacts` })
				},
				legalPeoples: {
					queryKey: null,
					queryFn: () => cellFetcher<HydraResponse<ICellLegalPerson>>({ url: `companies/${companyId}/legal_peoples` })
				},
				orders: {
					queryKey: null,
					queryFn: () => cellFetcher<HydraResponse<ICellCompany>>({ url: `companies/${companyId}/orders` })
				},
				devices: (params?: TCellDeviceParams) => ({
					queryKey: [companyId || ''],
					queryFn: () =>
						cellFetcher<HydraResponse<ICellCompanyDevice>>({
							url: `companies/${companyId}/devices`,
							params
						})
				}),
				deliveryAddress: {
					queryKey: null,
					queryFn: () =>
						cellFetcher<HydraResponse<ICellDeliveryAddress>>({
							url: `companies/${companyId}/delivery_addresses`
						})
				},
				fleetManagers: (params?: TCellFleetParam) => ({
					queryKey: [params || ''],
					queryFn: () =>
						cellFetcher<HydraResponse<ICellFleetManager>>({
							url: `companies/${companyId}/fleet_managers` || '',
							params
						})
				}),
				simCards: (params: TCellSimCardParams) => ({
					queryKey: [companyId],
					queryFn: () =>
						cellFetcher<HydraResponse<ICellCompanySimCard>>({
							url: `companies/${companyId}/sim_cards`,
							params
						})
				})
			}
		};
	}
});
