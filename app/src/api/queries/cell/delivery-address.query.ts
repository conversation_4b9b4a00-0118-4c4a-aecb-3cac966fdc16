// utils
import { createQuery<PERSON>eys } from '@lukemorales/query-key-factory';
import { parseIri } from '@/api/fetcher';
// api
import type { ICellDeliveryAddress } from '@/api/interface/cell';
import { cellFetcher } from '@/api/fetcher';

// ----------------------------------------------------------------------

export const cellDeliveryAddressQueryKeys = createQueryKeys('cell_deliveryAddress', {
	byId: (id?: string) => {
		const deliveryAddressId = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [deliveryAddressId],
			queryFn: () => cellFetcher<ICellDeliveryAddress>({ url: `delivery_addresses/${deliveryAddressId}` })
		};
	}
});
