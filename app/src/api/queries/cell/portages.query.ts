// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
// api
import type { ICellPortage } from '@/api/interface/cell';
import { cellFetcher } from '@/api/fetcher';
import type { TCellPortageParams } from '@/types/params';
import type { HydraResponse } from '@/types';

// ----------------------------------------------------------------------

export const cellPortageQueryKeys = createQueryKeys('cell_portage', {
	all: (params?: TCellPortageParams) => ({
		queryKey: [params || ''],
		queryFn: () => cellFetcher<HydraResponse<ICellPortage>>({ url: 'portages', params })
	})
});
