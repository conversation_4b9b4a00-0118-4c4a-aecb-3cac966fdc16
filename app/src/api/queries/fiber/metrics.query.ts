// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
// api
import type { IFiberMetricsAppointment } from '@/api/interface/fiber';
import { fiberFetcher } from '@/api/fetcher';
// types
import type { TFiberMetricsAppoinmentCountParams } from '@/types/params';

// ----------------------------------------------------------------------

export const fiberMetricsQueryKeys = createQueryKeys('metrics', {
	appointmentCount: (params: TFiberMetricsAppoinmentCountParams) => ({
		queryKey: [params],
		queryFn: () => fiberFetcher<Record<string, IFiberMetricsAppointment>>({ url: '/metrics/appointments/count', params })
	}),
	liveONU: (params: { oltIp?: string }) => ({
		queryKey: [params],
		queryFn: () => fiberFetcher({ url: '/metrics/live/onu', params })
	}),
	liveBox: (params: { ref?: string }) => ({
		queryKey: [params],
		queryFn: () => fiberFetcher({ url: '/metrics/live/freebox', params })
	})
});
