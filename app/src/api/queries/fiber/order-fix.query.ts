// utils
import { createQueryKeys } from '@lukemorales/query-key-factory';
// api
import type { IOrderItemFix } from '@/api/interface/fiber';
import { fiberFetcher, parseIri } from '@/api/fetcher';
import type { TFiberGetOrderFixParams } from '@/types/params';
import type { HydraResponse } from '@/types';

// ----------------------------------------------------------------------

export const fiberOrderFixQueryKeys = createQueryKeys('fiber_orderFix', {
	all: (params: TFiberGetOrderFixParams) => ({
		queryKey: [params],
		queryFn: () => fiberFetcher<HydraResponse<IOrderItemFix>>({ url: 'orders_fixes', params })
	}),
	byId: (id?: string) => {
		const orderId = id ? (parseIri(id) as string) : '';

		return {
			queryKey: [orderId],
			queryFn: () => fiberFetcher<IOrderItemFix>({ url: `orders/fixes?${orderId}` })
		};
	}
});
