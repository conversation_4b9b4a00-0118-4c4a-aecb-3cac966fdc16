// utils
import { z } from 'zod';
// api
import { dateSchema } from '@/api/schemas/common';

// ----------------------------------------------------------------------

export const magnetDocumentEntitySchema = z
	.object({
		/** @param message_id (table message) */
		message: z.string(),
		extension: z.string(),
		uuid: z.string()
	})
	.merge(
		dateSchema.omit({
			expectedAt: true
		})
	)
	.required()
	.partial({
		deletedAt: true
	});

export type TMagnetDocumentEntitySchema = z.infer<typeof magnetDocumentEntitySchema>;
