// utils
import { z } from 'zod';
// api
import { dateSchema } from '@/api/schemas/common';

// ----------------------------------------------------------------------

export const magnetFormFieldEntitySchema = z
	.object({
		name: z.string(),
		type: z.string(),
		label: z.string(),
		/** @param regex_id Table regex */
		regex: z.string().nullable(),
		/** @param form_id Table form */
		form: z.string()
	})
	.merge(
		dateSchema.omit({
			expectedAt: true
		})
	)
	.required();

export type TMagnetFormFieldEntitySchema = z.infer<typeof magnetFormFieldEntitySchema>;
