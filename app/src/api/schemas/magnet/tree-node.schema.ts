// utils
import { z } from 'zod';
// api
import { dateSchema } from '@/api/schemas/common';

// ----------------------------------------------------------------------

export const magnetTreeNodeEntitySchema = z
	.object({
		name: z.string(),
		/** @param domain_id (PAWN table domain) */
		domain: z.string(),
		/** @param space_id Table space */
		space: z.string(),
		/** @param is_root */
		isRoot: z.boolean()
		// forms: z.array().nullable()
	})
	.merge(
		dateSchema.omit({
			expectedAt: true
		})
	)
	.required();
/** @remark relation ManyOfMany TreeNode */
export const magnetTreeBranchEntitySchema = z
	.object({
		/** @param parent_tree_node_id Table TreeNode */
		parentTreeNode: z.string(),
		/** @param tree_node_id Table TreeNode */
		treeNode: z.string()
	})
	.merge(
		dateSchema.pick({
			createdAt: true,
			deletedAt: true
		})
	)
	.required();

export type TMagnetTreeNodeEntitySchema = z.infer<typeof magnetTreeNodeEntitySchema>;
export type TMagnetTreeBranchEntitySchema = z.infer<typeof magnetTreeBranchEntitySchema>;
