// api
import type { TCreatedAtParams, TPaginationParams, TSort } from '../common';
import type { TTicketPriority, TTicketState } from '@/api/enums/magnet';

// ----------------------------------------------------------------------

export type TGetTicketsParams = Partial<
	{
		assignedTo: string;
		'assignedTo[]': string[];
		'exists[deletedAt]': boolean;
		'order[createdAt]': TSort;
		'order[id]': TSort;
		'order[reference]': TSort;
		'order[state]': TSort;
		'order[updatedAt]': TSort;
		priority: TTicketPriority;
		'priority[]': TTicketPriority[];
		reference: string;
		'reference[]': string[];
		search: string;
		state: TTicketState;
		'state[]': TTicketState[];
		type: string;
		'user.createdByName': string;
		'user.createdByName[]': string[];
		'user.domain': string;
		'user.domain[]': string[];
		'user.team': string;
		'user.team[]': string[];
		'user.uri': string;
		'user.uri[]': string[];
	} & TCreatedAtParams &
		TPaginationParams
>;
