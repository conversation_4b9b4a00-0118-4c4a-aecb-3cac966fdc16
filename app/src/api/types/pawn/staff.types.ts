// types
import type { TPaginationParams } from '@/types/fetcher.types';

// ----------------------------------------------------------------------

/* Login */
export type TPawnStaffLoginFetchData = {
	uid: string;
	password: string;
};
export type TPawnStaffLoginFetchResponse = {
	code: number;
	payload: {
		token: string;
	};
};

type TOrderBy = 'asc' | 'desc';

export type TPawnStaffParams = Partial<
	{
		id: number;
		'id[]': number[];
		username: string;
		email: string;
		'job.type': string;
		pagination: boolean;
		firstName: string;
		lastName: string;
		'job.type[]': string[];
		'job.name': string;
		'job.name[]': string[];
		'teams.team.name': string;
		'teams.team.name[]': string[];
		'teams.team.domain.name': string;
		'teams.team.domain.name[]': string[];
		'orderBy[id]': TOrderBy;
		'orderBy[lastName]': TOrderBy;
		'orderBy[firstName]': TOrderBy;
		'orderBy[job.name]': TOrderBy;
		'orderBy[teams.team.name]': TOrderBy;
		roles: string;
		'roles[]': string[];
		search: string;
	} & TPaginationParams
>;
