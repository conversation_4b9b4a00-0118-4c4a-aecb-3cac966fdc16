// api
import type { IAbstractUser } from '@/api/interface/pawn';

// ----------------------------------------------------------------------

export type ActionMapType<M extends { [index: string]: any }> = {
	[Key in keyof M]: M[Key] extends undefined
		? {
				type: Key;
			}
		: {
				type: Key;
				payload: M[Key];
			};
};

export type AuthUserType = (IAbstractUser & Record<string, any>) | null;
export type AuthUserDetailsType = IAbstractUser | null;

export type AuthStateType = {
	status?: string;
	loading: boolean;
	user: AuthUserType;
	userDetails: AuthUserDetailsType;
};

// ----------------------------------------------------------------------

export enum AuthTypes {
	INITIAL = 'INITIAL',
	LOGIN = 'LOGIN',
	TOKEN_CHECK = 'TOKEN_CHECK',
	ME = 'ME',
	REGISTER = 'REGISTER',
	LOGOUT = 'LOGOUT'
}

export type AuthPayload = {
	[AuthTypes.INITIAL]: {
		user: AuthUserType;
	};
	[AuthTypes.LOGIN]: {
		user: AuthUserType | IUser;
	};
	[AuthTypes.ME]: {
		user: AuthUserType | IUser;
		userDetails: IAbstractUser | null;
	};
	[AuthTypes.REGISTER]: {
		user: AuthUserType | IUser;
	};
	[AuthTypes.LOGOUT]: undefined;
};

export type AuthActionsType = ActionMapType<AuthPayload>[keyof ActionMapType<AuthPayload>];

// ----------------------------------------------------------------------

export interface IUserSentry {
	id?: string;
	username: string;
	email: string;
	ip_address: string;
}

export interface IUser {
	uid: string;
	email: string;
	username: string;
	firstName: string;
	lastName: string;
	roles: string[];
	jwtCreatedAt: string;
	jwtExpiredAt: string;
}

export interface IAuthContext {
	user: AuthUserType;
	userDetails: AuthUserDetailsType;
	method: string;
	loading: boolean;
	authenticated: boolean;
	unauthenticated: boolean;
	login: (email: string, password: string) => Promise<void>;
	fetchMe: () => Promise<void>;
	register: (email: string, password: string, firstName: string, lastName: string) => Promise<void>;
	logout: () => Promise<void>;
}
