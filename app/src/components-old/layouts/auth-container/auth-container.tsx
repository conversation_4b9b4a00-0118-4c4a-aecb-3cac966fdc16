import type { ReactElement } from 'react';
// utils
import Spline from '@splinetool/react-spline';
import { twJoin } from 'tailwind-merge';

// ----------------------------------------------------------------------

type Props = {
	children: ReactElement;
};

export default function AuthContainer({ children }: Props) {
	return (
		<div
			className={twJoin(
				'relative h-full w-full flex lg:flex-row flex-col-reverse dark:bg-free-dark-mode dark:text-gray-100 lg:h-screen items-center'
			)}>
			<div className='lg:w-1/2 h-full flex justify-center items-center'>{children}</div>
			<div className='lg:w-1/2 w-full h-full p-5  overflow-hidden '>
				<div className='rounded-3xl overflow-hidden w-full h-full transition-opacity ease-in duration-700 opacity-100 lg:max-h-full max-h-[500px]'>
					<Spline scene='https://prod.spline.design/LKudFQLjfPP4fb6M/scene.splinecode' className={'!h-[calc(100%+60px)]'} />
				</div>
			</div>
		</div>
	);
}
