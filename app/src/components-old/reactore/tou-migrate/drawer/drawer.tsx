import { type ReactNode, useEffect, useRef, useState } from 'react';
// utils
import { useOnClickOutside } from '@tools/reactore';
import { twJoin, twMerge } from 'tailwind-merge';
// components
import InPortal from '../../../ui/in-portal/in-portal';

// ----------------------------------------------------------------------

type TDrawerPosition = 'top' | 'right' | 'bottom' | 'left';

type Props = {
	isOpen: boolean;
	onClose: () => void;
	children: ReactNode;
	position?: TDrawerPosition;
};

export default function Drawer({ isOpen, onClose, children, position = 'right' }: Props) {
	const drawerRef = useRef<HTMLDivElement>(null);

	const [hasTransitionedIn, setHasTransitionedIn] = useState(false);

	useOnClickOutside(drawerRef, () => {
		if (onClose) {
			onClose();
		}
	});

	useEffect(() => {
		let unmountTimeoutId: NodeJS.Timeout;

		if (isOpen && !hasTransitionedIn) {
			setHasTransitionedIn(true);
		} else if (!isOpen && hasTransitionedIn) {
			unmountTimeoutId = setTimeout(() => setHasTransitionedIn(false), 300);
		}

		return () => {
			clearTimeout(unmountTimeoutId);
		};
	}, [isOpen, hasTransitionedIn]);

	const getTransition = (position: TDrawerPosition) => {
		switch (position) {
			case 'bottom':
				return {
					from: 'translate-y-full',
					to: 'translate-y-0',
					animate: 'animate-fromBottom'
				};
			case 'left':
				return {
					from: '-translate-x-full',
					to: 'translate-x-0',
					animate: 'animate-fromLeft'
				};
			case 'top':
				return {
					from: '-translate-y-full',
					to: 'translate-y-0',
					animate: 'animate-fromTop'
				};
			default:
				return {
					from: 'translate-x-full',
					to: 'translate-x-0',
					animate: 'animate-fromRight'
				};
		}
	};

	return (
		<>
			{(isOpen || hasTransitionedIn) && (
				<InPortal selector='main'>
					<div className={twJoin('backdrop fixed z-[1500] h-full w-full backdrop-blur')}>
						<div className={twJoin('relative h-full w-full', isOpen && getTransition(position).animate)}>
							<div
								ref={drawerRef}
								className={twMerge(
									'ease absolute bg-white p-4 duration-[350ms]',
									position === 'left' && 'left-0 h-full rounded-r-xl',
									position === 'right' && 'right-0 h-full rounded-l-xl',
									position === 'top' && 'top-0 w-full rounded-b-xl',
									position === 'bottom' && 'bottom-0 w-full rounded-t-xl',
									isOpen && hasTransitionedIn
										? `opacity-100 ${getTransition(position).to}`
										: `opacity-0 ${getTransition(position).from}`
								)}>
								{children}
							</div>
						</div>
					</div>
				</InPortal>
			)}
		</>
	);
}
