import { useEffect, useRef, useState } from 'react';
// utils
import { twJoin, twMerge } from 'tailwind-merge';

// ----------------------------------------------------------------------

type Props = {
	checkedIndex: number;
	onToggle: (index: number) => void;
	texts?: [string, string, string];
	className?: string;
};
// TODO: rename Switch | SwitchButton | SwitchTab
export default function SwitchXXL({ checkedIndex, onToggle, texts, className }: Props) {
	const parentRef = useRef<HTMLDivElement>(null);
	const buttonsRef = useRef<HTMLButtonElement[]>([]);

	const [leftPosition, setLeftPosition] = useState(0);
	const [height, setHeight] = useState(0);
	const [width, setWidth] = useState(0);

	useEffect(() => {
		if (buttonsRef.current[checkedIndex]) {
			const parentLeft = parentRef.current?.getBoundingClientRect().left;
			const left = buttonsRef.current[checkedIndex]?.getBoundingClientRect().left;

			const height = buttonsRef.current[checkedIndex]?.getBoundingClientRect().height;

			const width = buttonsRef.current[checkedIndex]?.getBoundingClientRect().width;

			if (typeof left === 'number' && typeof parentLeft === 'number') {
				setLeftPosition(left - parentLeft);
			}

			if (height) {
				setHeight(height - 6);
			}

			if (width) {
				setWidth(width);
			}
		}
	}, [checkedIndex, buttonsRef]);

	return (
		<div
			ref={parentRef}
			className={twMerge('relative flex h-12 w-max items-center rounded-full bg-free-gray-50 px-1.5 py-0.25 shadow-lg', className)}>
			<div
				className={twJoin('absolute z-10 rounded-4xl bg-white transition-[left] duration-300')}
				style={{ left: leftPosition, height, width }}
			/>
			{texts?.map((text, index) => (
				<button
					key={index}
					ref={(el) => {
						if (buttonsRef.current && el) {
							buttonsRef.current[index] = el;
						}
					}}
					onClick={() => onToggle(index)}
					type='button'
					className={twMerge(
						'z-20 h-full w-auto rounded-4xl px-8 py-2 text-sm font-normal transition-colors duration-300',
						checkedIndex === index ? 'font-semibold text-free-red' : 'font-bold text-free-gray-400'
					)}>
					{text}
				</button>
			))}
		</div>
	);
}
