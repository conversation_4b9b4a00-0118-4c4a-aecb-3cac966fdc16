<svg width="1245" height="941" viewBox="0 0 1245 941" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_1935_31025)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M633.943 217C682.401 216.922 706.27 279.443 750.234 300.377C811.962 329.769 913.678 298.186 939.889 362.68C965.264 425.117 890.187 485.515 845.79 535.496C815.357 569.758 769.99 579.459 729.36 599.692C697.524 615.546 669.319 640.452 633.943 640.993C598.271 641.539 571.469 609.584 536.439 602.642C457.512 587 327.104 654.016 302.699 575.345C278.224 496.448 426.924 477.069 477.476 412.569C502.36 380.821 492.723 332.241 517.916 300.751C548.682 262.297 585.487 217.078 633.943 217Z" fill="url(#paint0_linear_1935_31025)" fill-opacity="0.25"/>
</g>
<defs>
<filter id="filter0_f_1935_31025" x="0" y="-83" width="1245" height="1024" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_1935_31025"/>
</filter>
<linearGradient id="paint0_linear_1935_31025" x1="688" y1="594.5" x2="502.5" y2="310" gradientUnits="userSpaceOnUse">
<stop stop-color="#4C68CE"/>
<stop offset="1" stop-color="#EC7272"/>
</linearGradient>
</defs>
</svg>
