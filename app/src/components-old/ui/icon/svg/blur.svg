<svg width="1032" height="875" viewBox="0 0 1032 875" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_3427_76865)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M523.664 291C556.12 290.948 572.106 332.825 601.552 346.847C642.896 366.534 711.021 345.38 728.577 388.578C745.572 430.399 695.288 470.854 665.553 504.332C645.169 527.281 614.784 533.779 587.571 547.332C566.249 557.951 547.358 574.633 523.664 574.995C499.772 575.361 481.821 553.957 458.359 549.307C405.497 538.83 318.153 583.718 301.808 531.023C285.415 478.178 385.009 465.197 418.868 421.995C435.534 400.729 429.079 368.19 445.953 347.098C466.559 321.341 491.21 291.052 523.664 291Z" fill="url(#paint0_linear_3427_76865)" fill-opacity="0.35"/>
</g>
<defs>
<filter id="filter0_f_3427_76865" x="0" y="-9" width="1032" height="884" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_3427_76865"/>
</filter>
<linearGradient id="paint0_linear_3427_76865" x1="516" y1="291" x2="516" y2="575" gradientUnits="userSpaceOnUse">
<stop stop-color="#4C68CE"/>
<stop offset="1" stop-color="#EC7272"/>
</linearGradient>
</defs>
</svg>
