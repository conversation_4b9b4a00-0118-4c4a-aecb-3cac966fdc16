import type { ReactNode } from 'react';
// utils
import { twJoin } from 'tailwind-merge';

// ----------------------------------------------------------------------

type Props = {
	activeTab: number;
	index: number;
	children: ReactNode;
};

export default function TabPanel({ index, activeTab, children }: Props) {
	return <div className={twJoin(activeTab === index ? 'visible' : 'hidden')}>{children}</div>;
}
