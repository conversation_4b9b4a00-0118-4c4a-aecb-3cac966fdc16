import { type CSSProperties, useState } from 'react';
// tanstack
import {
	type ColumnDef,
	flexRender,
	getCoreRowModel,
	useReactTable,
	getSortedRowModel,
	type SortingState,
	type ColumnFiltersState,
	getFilteredRowModel,
	type Row,
	getPaginationRowModel,
	type OnChangeFn,
	type Cell,
	type Header,
	type VisibilityState,
	type ColumnOrderState,
	type Column
} from '@tanstack/react-table';
// utils
import {
	closestCenter,
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	MouseSensor,
	TouchSensor,
	useSensor,
	useSensors
} from '@dnd-kit/core';
import { restrictToHorizontalAxis } from '@dnd-kit/modifiers';
import { arrayMove, horizontalListSortingStrategy, SortableContext, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { atomWithStorage } from 'jotai/utils';
import { X } from 'lucide-react';
// components
import CustomDropDwonMenuFilter from '../custom/custom-drop-down-menu-filter';
import { Input } from '../ui/input';
import { CustomFilter, type CustomFilterProps } from '@/components/custom';
import { DataTablePagination } from '@/components/custom-table';
import { SearchBar } from '@/components/search-bar';
import { Button } from '@/components/ui/button.tsx';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table.tsx';
import { DotsVertical } from '@tools/reactor-icons';

// ----------------------------------------------------------------------

type PaginationState = {
	pageIndex: number;
	pageSize: number;
};

type TColumnsDataByKey<T> = { [key in T as string]: { title: string } };

export type ExtendedColumnDef<T> = ColumnDef<T> & {
	id: string;
	title: string;
};

type ColumnDataItem = {
	title: string;
};

export type TColumnDataByKey = Record<string, ColumnDataItem>;

interface DataTableProps<TData> {
	columns: ColumnDef<TData>[];
	columnsDataByKey: TColumnsDataByKey<TData>;
	defaultColumnVisibility?: VisibilityState | undefined;
	columnVisibility: {
		columnVisibility: VisibilityState | undefined;
		setColumnVisibility: OnChangeFn<VisibilityState>;
	};
	columnOrder: {
		columnOrder: ColumnOrderState;
		setColumnOrder: OnChangeFn<ColumnOrderState> | undefined;
	};
	data: TData[];
	isLoading?: boolean;
	linkId?: string;
	isCheckable?: boolean;
	isSearchable?: boolean;
	isPaginated?: boolean;
	isFixed?: boolean;
	filters?: CustomFilterProps[];
	actions?: React.ReactNode;
	searchValue?: string;
	onSearch?: (value: string) => void;
	sorting?: SortingState;
	onSortingChange?: (sorting: SortingState) => void;
	selectedRowIds?: string[];
	onRowSelectionChange?: (selectedIds: string[]) => void;
	totalFilters?: number;
	onReset?: () => void;
	getRowId?: (row: TData) => string;
	onRowClick?: (row: TData) => void;
	paginationState?: PaginationState;
	onPaginationChange?: (state: PaginationState) => void;
	totalRows?: number;
}

export const handleInitVisibilityAtomWithStorage = (nameAtom: string) => {
	return atomWithStorage<VisibilityState | undefined>(nameAtom, {});
};

export function handleInitDndAtomWithStorage<T>(nameAtom: string, columns: ExtendedColumnDef<T>[]) {
	return atomWithStorage<string[]>(
		nameAtom,
		columns.map((c) => c.id!)
	);
}

export function handleInitColumnsDataByKey<T>(columns: ExtendedColumnDef<T>[]): TColumnDataByKey {
	return columns.reduce(
		(acc, column) => ({
			...acc,
			[column.id]: { title: column.title }
		}),
		{} as TColumnDataByKey
	);
}

type TableRowClickEvent = React.MouseEvent<HTMLTableRowElement, MouseEvent>;

export default function DataTableDnd<TData>({
	columns,
	columnsDataByKey,
	columnVisibility,
	columnOrder,
	data,
	isLoading,
	isSearchable = false,
	isFixed = false,
	filters = [],
	actions,
	searchValue = '',
	onSearch,
	totalFilters: totalFiltersTmp = 0,
	onReset,
	onRowClick,
	isPaginated = false,
	paginationState,
	onPaginationChange,
	totalRows
}: DataTableProps<TData>) {
	const [sorting, setSorting] = useState<SortingState>([]);

	const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

	const [pagination, setPagination] = useState<PaginationState>({
		pageIndex: 0,
		pageSize: 10
	});

	const handlePaginationChange: OnChangeFn<PaginationState> = (updaterOrValue) => {
		const newPagination = typeof updaterOrValue === 'function' ? updaterOrValue(paginationState || pagination) : updaterOrValue;

		if (onPaginationChange) {
			onPaginationChange(newPagination);
		} else {
			setPagination(newPagination);
		}
	};

	const table = useReactTable({
		data,
		columns,
		getCoreRowModel: getCoreRowModel(),
		onSortingChange: setSorting,
		getSortedRowModel: getSortedRowModel(),
		onColumnFiltersChange: setColumnFilters,
		onColumnVisibilityChange: columnVisibility.setColumnVisibility,
		onColumnOrderChange: columnOrder.setColumnOrder,
		getFilteredRowModel: getFilteredRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		pageCount: totalRows ? Math.ceil(totalRows / (paginationState?.pageSize || pagination.pageSize)) : undefined,
		onPaginationChange: handlePaginationChange,
		manualPagination: !!onPaginationChange,
		state: {
			sorting,
			columnFilters,
			pagination: paginationState || pagination,
			columnOrder: columnOrder.columnOrder,
			columnVisibility: columnVisibility.columnVisibility
		},

		debugTable: true,
		debugHeaders: true,
		debugColumns: true
	});

	const handleRowClick = (event: TableRowClickEvent, row: Row<TData>) => {
		const target = event.target as HTMLElement;
		const isCheckboxClick = target.matches('[role="checkbox"]') || target.closest('[role="checkbox"]') !== null;

		if (!isCheckboxClick) {
			onRowClick?.(row.original);
		}
	};

	const DraggableTableHeader = ({
		header
	}: {
		header: Header<TData, unknown>;
	}) => {
		const { attributes, isDragging, listeners, setNodeRef, transform } = useSortable({
			id: header.column.id
		});

		const canSort = header.column.getCanSort();

		const cursor = canSort ? (isDragging ? 'grabbing' : 'grab') : 'pointer';

		const style: CSSProperties = {
			opacity: isDragging ? 0.8 : 1,
			position: 'relative',
			transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
			transition: 'width transform 0.2s ease-in-out',
			whiteSpace: 'nowrap',
			cursor,
			width: isFixed ? `${header.getSize()}px` : 'auto',
			zIndex: isDragging ? 1 : 0
		};

		return (
			<TableHead colSpan={header.colSpan} ref={canSort ? setNodeRef : null} key={header.id} style={style}>
				<div className={`cursor-${cursor}`} {...(canSort ? attributes : {})} {...(canSort ? listeners : {})}>
					{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
				</div>
			</TableHead>
		);
	};

	const DragAlongCell = ({ cell }: { cell: Cell<TData, unknown> }) => {
		const { isDragging, setNodeRef, transform } = useSortable({
			id: cell.column.id
		});

		const style: CSSProperties = {
			opacity: isDragging ? 0.8 : 1,
			position: 'relative',
			transform: CSS.Translate.toString(transform), // translate instead of transform to avoid squishing
			transition: 'width transform 0.2s ease-in-out',
			width: cell.column.getSize(),
			zIndex: isDragging ? 1 : 0
		};

		return (
			<TableCell key={cell.id} style={style} ref={cell.column.getCanSort() ? setNodeRef : null}>
				{flexRender(cell.column.columnDef.cell, cell.getContext())}
			</TableCell>
		);
	};
	function handleDragEnd(event: DragEndEvent) {
		const { active, over } = event;
		if (active && over && active.id !== over.id && columnOrder.setColumnOrder) {
			columnOrder.setColumnOrder((columnOrder) => {
				const oldIndex = columnOrder.indexOf(active.id as string);
				const newIndex = columnOrder.indexOf(over.id as string);
				return arrayMove(columnOrder, oldIndex, newIndex); //this is just a splice util
			});
		}
	}

	const sensors = useSensors(useSensor(MouseSensor, {}), useSensor(TouchSensor, {}), useSensor(KeyboardSensor, {}));

	const resetFilter = () => {
		columnVisibility.setColumnVisibility({});
		if (onReset) onReset();
	};

	const totalFiltersVisibility = table.getAllLeafColumns().filter((column) => column.getIsVisible() === false).length;
	const totalFilters = totalFiltersTmp + totalFiltersVisibility + (searchValue?.length || 0);

	return (
		<div className='space-y-4'>
			<DndContext
				collisionDetection={closestCenter}
				modifiers={[restrictToHorizontalAxis]}
				onDragEnd={handleDragEnd}
				sensors={sensors}>
				<div className='flex justify-between gap-2'>
					{isSearchable && (
						<div className='pt-1 min-w-40'>
							<SearchBar value={searchValue} onChange={(value: any) => onSearch?.(value)} placeholder='Rechercher...' />{' '}
						</div>
					)}
					<div className='justify-between flex-1 gap-2 pt-1 overflow-y-auto rounded-xl flex-nowrap'>
						<div className='flex gap-2 flex-nowrap lg:flex-nowrap justify-between'>
							<div className='flex gap-2 flex-nowrap lg:flex-nowrap'>
								{filters.map((filter, index) => (
									<CustomFilter key={index} {...filter} />
								))}
							</div>
							<CustomDropDwonMenuFilter<Column<TData, unknown>>
								footer={{ title: 'Effacer ce filtre', onClick: () => columnVisibility.setColumnVisibility({}) }}
								numberActivated={totalFiltersVisibility}
								data={table
									.getAllLeafColumns()
									.map((column) => ({ ...column, idTmp: columnsDataByKey?.[column.id].title }))}
								pathKey={'idTmp'}
								titleButton={''}
								className='bg-transparent hover:bg-transparent p-0'
								customIcon={<DotsVertical />}
								render={(items) => (
									<div className='flex flex-col gap-2'>
										{items.map((column) => {
											return (
												<div key={column.id} className='px-1'>
													<label className='flex items-center gap-2' htmlFor={`column-${column.id}`}>
														<Input
															{...{
																type: 'checkbox',
																disabled: !column.getCanHide(),
																className: 'p-0 m-0 w-4.5 h-4.5',
																checked: column.getIsVisible(),
																onChange: column.getToggleVisibilityHandler(),
																id: `column-${column.id}`
															}}
														/>
														<>{columnsDataByKey?.[column.id].title}</>
													</label>
												</div>
											);
										})}
									</div>
								)}
							/>
						</div>
					</div>

					<div className='flex gap-2 pt-1'>
						{totalFilters > 0 && (
							<Button variant='ghost' onClick={resetFilter} className='h-8 px-2'>
								<X className='w-3 h-3 mr-2' />
								Effacer les filtres
							</Button>
						)}
						{actions}
					</div>
				</div>
				<div className='rounded-lg bg-card'>
					<Table>
						<TableHeader>
							{table.getHeaderGroups().map((headerGroup) => (
								<TableRow key={headerGroup.id}>
									<SortableContext items={columnOrder.columnOrder} strategy={horizontalListSortingStrategy}>
										{headerGroup.headers.map((header) => (
											<DraggableTableHeader key={header.id} header={header} />
										))}
									</SortableContext>
								</TableRow>
							))}
						</TableHeader>

						<TableBody>
							{isLoading ? (
								<TableRow>
									<TableCell colSpan={columns.length} className='h-24 text-center'>
										Chargement des données...
									</TableCell>
								</TableRow>
							) : table.getRowModel().rows.length ? (
								table.getRowModel().rows.map((row) => (
									<TableRow
										key={row.id}
										className={'cursor-pointer'}
										data-state={row.getIsSelected() && 'selected'}
										onClick={(e) => handleRowClick(e, row)}>
										<SortableContext items={columnOrder.columnOrder} strategy={horizontalListSortingStrategy}>
											{row.getVisibleCells().map((cell) => (
												<DragAlongCell key={cell.id} cell={cell} />
											))}
										</SortableContext>
									</TableRow>
								))
							) : (
								<TableRow>
									<TableCell colSpan={columns.length} className='h-24 text-center'>
										Aucun résultat.
									</TableCell>
								</TableRow>
							)}
						</TableBody>
					</Table>
				</div>

				{isPaginated && <DataTablePagination table={table} />}
			</DndContext>
		</div>
	);
}
