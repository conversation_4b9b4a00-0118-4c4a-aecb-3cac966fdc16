// utils
import HTMLReactParser from 'html-react-parser';
import { AvatarFallback } from '@radix-ui/react-avatar';
// hooks
import { useAvatar } from '@/hooks/useAvatar.ts';
// components
import { Avatar, AvatarImage } from '@/components/ui/avatar.tsx';
// others
import { cn } from '@/lib/utils.ts';

// ----------------------------------------------------------------------

type Props = {
	src?: string;
	name?: string;
	className?: string;
	option?: string;
};

export default function CustomAvatar({ src, name, className, option }: Props) {
	const { img } = useAvatar({ seed: name, option });

	const { svg: svgDefault } = useAvatar({ seed: name });

	return (
		<Avatar className={cn('h-8 w-8', className)}>
			<AvatarImage src={src || img} alt={`avatar ${name}`} />
			<AvatarFallback className='h-8 w-8'>{svgDefault && HTMLReactParser(svgDefault)}</AvatarFallback>
		</Avatar>
	);
}
