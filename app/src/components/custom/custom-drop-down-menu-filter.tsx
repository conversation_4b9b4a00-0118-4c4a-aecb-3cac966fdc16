import type { ReactElement } from 'react';
// utils
import { Plus, X } from 'lucide-react';
// hooks
import { useSearch } from '@/hooks/use-Search';
// components
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent } from '../ui/dropdown-menu';
import { Button } from '@/components/ui/button.tsx';
import { cn } from '@/lib/utils.ts';

// ----------------------------------------------------------------------

type Props<T> = {
	render(data: T[]): ReactElement;
	data: T[];
	pathKey: string;
	titleButton: string;
	numberActivated?: number;
	className?: string;
	customIcon?: ReactElement;
};

export default function CustomDropDwonMenuFilter<T>({
	render,
	data,
	pathKey,
	titleButton,
	numberActivated,
	className,
	customIcon
}: Props<T>) {
	const { filteredData, handleFilterChange, search } = useSearch<T>({ pathKey, data });

	return (
		<DropdownMenu>
			<Button variant='neutral' size='sm' className={cn('relative gap-2 bg-card rounded-full cursor-default', className)}>
				{numberActivated !== undefined && numberActivated !== 0 && (
					<div className='absolute flex justify-center items-center  bg-blue-600  w-4 h-4 -right-2 -top-1 rounded-3xl text-xs text-white'>
						{numberActivated}
					</div>
				)}
				<div className='flex gap-1.5'>
					<DropdownMenuTrigger asChild>
						<div className='flex gap-1 cursor-pointer items-center'>
							{customIcon ? customIcon : <Plus className='h-4 w-4' />}
							{titleButton}
						</div>
					</DropdownMenuTrigger>
				</div>
			</Button>

			<DropdownMenuContent className='p-0 rounded-2xl -translate-x-3 translate-y-2' align='start'>
				<div className='flex items-center border-b p-2'>
					{/* <Search className='ml-2 h-4 w-4 shrink-0 text-muted-foreground' /> */}
					<input
						placeholder='Rechercher une colonne'
						value={search}
						onChange={(e) => handleFilterChange(e.target.value)}
						className='flex h-8 w-full rounded-md bg-transparent py-2 text-sm outline-none border-none focus:ring-0 placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50'
					/>
					<Button
						variant='ghost'
						size='sm'
						className='justify-center w-6 h-6 text-muted-foreground rounded-xl'
						onClick={() => handleFilterChange('')}>
						<X className='h-4 w-4' />
					</Button>
				</div>
				<div className='p-4'>{render(filteredData)}</div>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
