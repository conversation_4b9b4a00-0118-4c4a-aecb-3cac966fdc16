import React from 'react';
// utils
import { cn } from '@/lib/utils';
import type { EraType } from '@/api/interface';

// ----------------------------------------------------------------------

interface StateBadgeProps {
	era: EraType;
	className?: string;
	label?: string;
}

const getEraFillPercentage = (era: EraType): number => {
	switch (era) {
		case 'pre_production':
			return 25;
		case 'production':
			return 50;
		case 'post_production':
			return 75;
		case 'sav':
			return 100;
		default:
			return 25;
	}
};

export const getEraLabel = (era: EraType): string => {
	switch (era) {
		case 'pre_production':
			return 'Pre Production';
		case 'production':
			return 'Production';
		case 'post_production':
			return 'Post Production';
		case 'sav':
			return 'SAV';
		default:
			return 'Pre Production';
	}
};

export default function StateBadge({ era, className, label }: StateBadgeProps) {
	const fillPercentage = getEraFillPercentage(era);
	const fillDegrees = (fillPercentage / 100) * 360;
	const displayLabel = label || getEraLabel(era);

	const uniqueId = React.useId().replace(/:/g, '');
	const clipPathId = `state_badge_clip_path_${uniqueId}`;

	return (
		<div
			className={cn(
				'bg-purple-200 rounded-md px-2.5 py-0.5 text-xs font-semibold text-[#6B21A8] flex gap-2 items-center',
				className
			)}>
			<svg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'>
				<g clipPath={`url(#${clipPathId})`}>
					<g transform='matrix(0 -0.00525014 0.00525014 0 6.99935 7.00033)'>
						<foreignObject x='-1527.74' y='-1527.74' width='3055.47' height='3055.47'>
							<div
								style={{
									background: `conic-gradient(from 90deg,rgba(107, 33, 168, 1) 0deg,rgba(107, 33, 168, 1) ${fillDegrees}deg,rgba(255, 255, 255, 0.5) ${fillDegrees}deg,rgba(255, 255, 255, 0.5) 360deg)`,
									height: '100%',
									width: '100%',
									opacity: 1
								}}></div>
						</foreignObject>
					</g>
				</g>
				<path d='M12.8327 7.00033H11.7389C11.7389 9.61793 9.61695 11.7399 6.99935 11.7399V12.8337V13.9274C10.8251 13.9274 13.9264 10.826 13.9264 7.00033H12.8327ZM6.99935 12.8337V11.7399C4.38175 11.7399 2.25977 9.61793 2.25977 7.00033H1.16602H0.0722656C0.0722656 10.826 3.17363 13.9274 6.99935 13.9274V12.8337ZM1.16602 7.00033H2.25977C2.25977 4.38273 4.38175 2.26074 6.99935 2.26074V1.16699V0.0732422C3.17363 0.0732422 0.0722656 3.1746 0.0722656 7.00033H1.16602ZM6.99935 1.16699V2.26074C9.61695 2.26074 11.7389 4.38273 11.7389 7.00033H12.8327H13.9264C13.9264 3.1746 10.8251 0.0732422 6.99935 0.0732422V1.16699Z' />
				<defs>
					<clipPath id={clipPathId}>
						<path d='M12.8327 7.00033H11.7389C11.7389 9.61793 9.61695 11.7399 6.99935 11.7399V12.8337V13.9274C10.8251 13.9274 13.9264 10.826 13.9264 7.00033H12.8327ZM6.99935 12.8337V11.7399C4.38175 11.7399 2.25977 9.61793 2.25977 7.00033H1.16602H0.0722656C0.0722656 10.826 3.17363 13.9274 6.99935 13.9274V12.8337ZM1.16602 7.00033H2.25977C2.25977 4.38273 4.38175 2.26074 6.99935 2.26074V1.16699V0.0732422C3.17363 0.0732422 0.0722656 3.1746 0.0722656 7.00033H1.16602ZM6.99935 1.16699V2.26074C9.61695 2.26074 11.7389 4.38273 11.7389 7.00033H12.8327H13.9264C13.9264 3.1746 10.8251 0.0732422 6.99935 0.0732422V1.16699Z' />
					</clipPath>
				</defs>
			</svg>
			{displayLabel}
		</div>
	);
}
