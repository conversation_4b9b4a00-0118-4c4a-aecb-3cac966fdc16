// utils
import { useFormContext } from 'react-hook-form';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '../ui/form';
import { Input } from '../ui/input';

// ----------------------------------------------------------------------
export type TSelectItem = {
	label: string;
	value: string;
};

type Props = {
	name: string;
	label?: string;
	placeholder?: string;
};

export function RHFInputText({ name, label, placeholder }: Props) {
	const { control } = useFormContext();

	return (
		<FormField
			control={control}
			name={name}
			render={({ field }) => (
				<FormItem className='grid w-full items-center gap-1.5'>
					{label && <FormLabel className='font-normal'>{label}</FormLabel>}
					<FormControl>
						<Input
							className='border-0 rounded-3xl bg-[#F3F4FC] py-2'
							placeholder={placeholder || ''}
							type={'text'}
							value={field.value}
							onChange={field.onChange}
						/>
					</FormControl>
					<FormMessage />
				</FormItem>
			)}
		/>
	);
}
