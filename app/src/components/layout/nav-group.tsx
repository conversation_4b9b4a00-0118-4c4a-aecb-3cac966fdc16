import type { ReactNode } from 'react';
// tanstack
import { Link, useLocation } from '@tanstack/react-router';
// utils
import { RIcon, type TNameIcons } from '@tools/reactor-icons';
import { ChevronRight } from 'lucide-react';
// contexts
import { useAuthContext } from '@/auth/auth-context';
// components
import { Badge } from '../ui/badge';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger
} from '../ui/dropdown-menu';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
	SidebarMenuItem,
	SidebarMenuButton,
	SidebarMenu,
	SidebarMenuSub,
	SidebarMenuSubItem,
	SidebarMenuSubButton,
	useSidebar
} from '@/components/ui/sidebar';
// types
import type { NavItem, NavLink, NavCollapsible } from './types';

// ----------------------------------------------------------------------

const NavBadge = ({ children }: { children: ReactNode }) => <Badge className='px-1 py-0 text-xs rounded-full'>{children}</Badge>;

const DynamicIcon = ({ iconName }: { iconName: string }) => {
	return <RIcon name={iconName as TNameIcons} size='16' />;
};

export function NavSection({ items }: { items: NavItem[] }) {
	const { state } = useSidebar();

	const { isAllowed } = useAuthContext();

	const href = useLocation({ select: (location) => location.href });

	// Filter items based on capabilities
	const filteredItems = items
		.filter((item) => {
			if (!item.capabilities) return true;
			return isAllowed(item.capabilities);
		})
		.map((item) => {
			if (item.items) {
				return {
					...item,
					items: item.items.filter((subItem) => !subItem.capabilities || isAllowed(subItem.capabilities))
				};
			}
			return item;
		});

	return (
		<SidebarMenu>
			{filteredItems.map((item) => {
				const key = `${item.title}-${item.url ?? ''}`;

				if (!item.items) return <SidebarMenuLink key={key} item={item} href={href} />;

				if (state === 'collapsed') return <SidebarMenuCollapsedDropdown key={key} item={item} href={href} />;

				return <SidebarMenuCollapsible key={key} item={item} href={href} />;
			})}
		</SidebarMenu>
	);
}

const SidebarMenuLink = ({ item, href }: { item: NavLink; href: string }) => {
	const { setOpenMobile } = useSidebar();
	const { url, icon, title, badge, search } = item;

	return (
		<SidebarMenuItem>
			<SidebarMenuButton asChild isActive={checkIsActive(href, item)} tooltip={item.title}>
				<Link to={url} search={search as any} onClick={() => setOpenMobile(false)}>
					{icon && <DynamicIcon iconName={icon} />}
					<span className='font-semibold'>{title}</span>
					{badge && <NavBadge>{badge}</NavBadge>}
				</Link>
			</SidebarMenuButton>
		</SidebarMenuItem>
	);
};

const SidebarMenuCollapsible = ({ item, href }: { item: NavCollapsible; href: string }) => {
	const { setOpenMobile } = useSidebar();

	return (
		<Collapsible asChild defaultOpen={checkIsActive(href, item, true)} className='group/collapsible'>
			<SidebarMenuItem>
				<CollapsibleTrigger asChild>
					<SidebarMenuButton tooltip={item.title}>
						{item.icon && <DynamicIcon iconName={item.icon} />}
						<span className='font-semibold'>{item.title}</span>
						{item.badge && <NavBadge>{item.badge}</NavBadge>}
						<ChevronRight className='ml-auto transition-transform duration-200 rotate-90 group-data-[state=open]/collapsible:-rotate-90' />
					</SidebarMenuButton>
				</CollapsibleTrigger>
				<CollapsibleContent className='CollapsibleContent'>
					<SidebarMenuSub>
						{item.items.map((subItem) => (
							<SidebarMenuSubItem key={subItem.title}>
								<SidebarMenuSubButton asChild isActive={checkIsActive(href, subItem)}>
									<Link to={subItem.url} search={subItem.search as any} onClick={() => setOpenMobile(false)}>
										{subItem.icon && <DynamicIcon iconName={subItem.icon} />}
										<span>{subItem.title}</span>
										{subItem.badge && <NavBadge>{subItem.badge}</NavBadge>}
									</Link>
								</SidebarMenuSubButton>
							</SidebarMenuSubItem>
						))}
					</SidebarMenuSub>
				</CollapsibleContent>
			</SidebarMenuItem>
		</Collapsible>
	);
};

const SidebarMenuCollapsedDropdown = ({ item, href }: { item: NavCollapsible; href: string }) => {
	return (
		<SidebarMenuItem>
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<SidebarMenuButton tooltip={item.title} isActive={checkIsActive(href, item)}>
						{item.icon && <DynamicIcon iconName={item.icon} />}
						<span className='font-semibold'>{item.title}</span>
						{item.badge && <NavBadge>{item.badge}</NavBadge>}
						<ChevronRight className='ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
					</SidebarMenuButton>
				</DropdownMenuTrigger>
				<DropdownMenuContent side='right' align='start' sideOffset={4}>
					<DropdownMenuLabel>
						{item.title} {item.badge ? `(${item.badge})` : ''}
					</DropdownMenuLabel>
					<DropdownMenuSeparator />
					{item.items.map((sub) => (
						<DropdownMenuItem key={`${sub.title}-${sub.url}`} asChild>
							<Link to={sub.url} search={sub.search as any} className={`${checkIsActive(href, sub) ? 'bg-secondary' : ''}`}>
								{sub.icon && <DynamicIcon iconName={sub.icon} />}
								<span className='font-semibold max-w-52 text-wrap'>{sub.title}</span>
								{sub.badge && <span className='ml-auto text-xs'>{sub.badge}</span>}
							</Link>
						</DropdownMenuItem>
					))}
				</DropdownMenuContent>
			</DropdownMenu>
		</SidebarMenuItem>
	);
};

function checkIsActive(href: string, item: NavItem, mainNav = false) {
	// Extract URL and search params from current location
	const currentUrl = href.split('?')[0];
	const currentSearch = new URLSearchParams(href.split('?')[1] || '');

	// Basic path matching
	const pathMatches = currentUrl === item.url || !!item?.items?.some((i) => i.url === currentUrl);

	// Check for main nav matching
	const mainNavMatches =
		mainNav && currentUrl.split('/')[1] !== '' && currentUrl.split('/')[1] === (item.url as string | undefined)?.split('/')[1];

	// If there are search params to check
	if (pathMatches && 'search' in item && item.search) {
		// Check if all search params match
		return Object.entries(item.search).every(([key, value]) => currentSearch.get(key) === value);
	}

	return pathMatches || mainNavMatches;
}
