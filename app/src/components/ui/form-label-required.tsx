import * as React from 'react';
// components
import { FormLabel } from './form';
// utils
import { cn } from '@/lib/utils';

// ----------------------------------------------------------------------

interface FormLabelRequiredProps extends React.ComponentPropsWithoutRef<typeof FormLabel> {
  required?: boolean;
}

const FormLabelRequired = React.forwardRef<
  React.ElementRef<typeof FormLabel>,
  FormLabelRequiredProps
>(({ className, children, required, ...props }, ref) => {
  return (
    <FormLabel ref={ref} className={cn(className)} {...props}>
      {children}
      {required && <span className="text-red-700 ml-1">*</span>}
    </FormLabel>
  );
});

FormLabelRequired.displayName = 'FormLabelRequired';

export { FormLabelRequired };
