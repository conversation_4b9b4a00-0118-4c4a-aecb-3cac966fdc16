import * as React from 'react';
// utils
import { Eye, EyeOff } from '@tools/reactor-icons';
// others
import { cn } from '@/lib/utils';

// ----------------------------------------------------------------------

interface InputProps extends React.ComponentProps<'input'> {
	variant?: 'default' | 'borderless';
	inputSize?: 'sm' | 'md' | 'lg';
	startDecorator?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
	({ className, type, variant = 'default', inputSize = 'md', startDecorator, ...props }, ref) => {
		const [showPassword, setShowPassword] = React.useState(false);

		const handleTogglePassword = () => {
			setShowPassword((prev) => !prev);
		};

		return (
			<>
				{startDecorator || type === 'password' ? (
					<div className={cn('relative flex items-center', inputSize === 'sm' && 'h-8', inputSize === 'lg' && 'h-12')}>
						{startDecorator && <div className='mr-2 absolute left-4'>{startDecorator}</div>}
						<input
							type={type === 'password' && showPassword ? 'text' : type}
							className={cn(
								'flex w-full rounded-md px-3 py-2 text-sm outline-none focus-visible:!shadow-none focus:border-primary transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
								variant === 'borderless'
									? 'border-0 rounded-2xl focus-visible:ring-0 dark:!bg-accent/50 '
									: 'border border-input bg-transparent ',
								inputSize === 'sm' && 'text-sm',
								inputSize === 'lg' && 'py-3 px-5',
								startDecorator && 'pl-12',
								className
							)}
							ref={ref}
							{...props}
						/>
						{type === 'password' && (
							// biome-ignore lint/a11y/useKeyWithClickEvents: Il veut préciser le keyup/keydown ? pas utile
							<div
								onClick={handleTogglePassword}
								className='cursor-pointer select-none absolute top-1/2 transform -translate-y-1/2  right-3 focus:outline-none text-foreground/50'>
								{showPassword ? <EyeOff size={20} strokeWidth={1.5} /> : <Eye size={20} strokeWidth={1.5} />}
							</div>
						)}
					</div>
				) : (
					<input
						type={type === 'password' && showPassword ? 'text' : type}
						className={cn(
							'flex w-full rounded-md px-3 py-2 text-sm outline-none focus-visible:!shadow-none focus:border-primary !ring-0 transition-colors placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50',
							variant === 'borderless'
								? 'border-0 rounded-2xl focus-visible:ring-0 dark:!bg-accent/50 '
								: 'border border-input bg-transparent ',
							inputSize === 'sm' && 'text-sm',
							inputSize === 'lg' && 'py-3 px-5',
							startDecorator && 'pl-12',
							className
						)}
						ref={ref}
						{...props}
					/>
				)}
			</>
		);
	}
);

Input.displayName = 'Input';

export { Input };
