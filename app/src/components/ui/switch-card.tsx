// utils
import { useFormContext } from 'react-hook-form';
// components
import { FormField } from '@/components/ui/form';
import { Badge, type TBadgeVariant } from '@/components/ui/badge';
// utils
import { cn } from '@/lib/utils';
import { RIcon, type TNameIcons } from '@tools/reactor-icons';

// ----------------------------------------------------------------------

interface SwitchCardProps {
	name: string;
	label: string;
	iconName?: TNameIcons;
	className?: string;
	disabled?: boolean;
	activeLabel?: string;
	inactiveLabel?: string;
	badgeVariant?: TBadgeVariant;
}

export function SwitchCard({
	name,
	label,
	iconName,
	className,
	disabled = false,
	activeLabel = 'Actif',
	inactiveLabel = 'Inactif',
	badgeVariant = 'green'
}: SwitchCardProps) {
	const { control } = useFormContext();

	return (
		<FormField
			control={control}
			name={name}
			render={({ field }) => {
				const handleClick = (e: React.MouseEvent) => {
					e.preventDefault();
					e.stopPropagation();
					if (!disabled) {
						field.onChange(!field.value);
					}
				};

				return (
					<div
						className={cn(
							'flex flex-row items-center justify-between rounded-lg border p-4 cursor-pointer transition-colors hover:bg-accent/20',
							disabled && 'opacity-50 cursor-not-allowed',
							className
						)}
						onClick={handleClick}>
						<div className='flex gap-3 items-center'>
							{/* Custom switch display to avoid Radix click handling */}
							<div
								className={cn(
									'relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
									field.value ? 'bg-primary' : 'bg-input',
									disabled && 'cursor-not-allowed opacity-50'
								)}>
								<span
									className={cn(
										'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-background shadow-lg ring-0 transition duration-200 ease-in-out',
										field.value ? 'translate-x-5' : 'translate-x-0'
									)}
								/>
							</div>
							<div className='flex items-center gap-2'>
								{iconName && <RIcon name={iconName} className='h-4 w-4' />}
								<span className='text-sm font-semibold select-none'>{label}</span>
							</div>
						</div>
						<Badge variant={field.value ? badgeVariant : 'gray'} size='sm'>
							{field.value ? activeLabel : inactiveLabel}
						</Badge>
					</div>
				);
			}}
		/>
	);
}
