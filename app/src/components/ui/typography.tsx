import { forwardRef, type JSX } from 'react';
// utils
import { cva, type VariantProps } from 'class-variance-authority';
// others
import { cn } from '@/lib/utils';

// ----------------------------------------------------------------------

const TitleStyles = cva([], {
	variants: {
		level: {
			1: 'text-7xl',
			2: 'text-6xl',
			3: 'text-5xl',
			4: 'text-4xl',
			5: 'text-3xl',
			6: 'text-2xl'
		},
		weight: {
			normal: 'font-normal',
			medium: 'font-medium',
			semibold: 'font-semibold',
			bold: 'font-bold'
		},
		color: {
			black: 'color-normal',
			gray: 'text-free-gray-500'
		},
		font: {
			montserrat: 'font-montserrat',
			iliad: 'font-iliad',
			ibmplexmono: 'font-ibmplexmono'
		}
	},
	defaultVariants: {
		color: 'black',
		font: 'montserrat'
	}
});

const TextStyles = cva([''], {
	variants: {
		size: {
			xl: 'text-xl',
			lg: 'text-lg',
			md: 'text-base',
			sm: 'text-sm',
			xs: 'text-xs'
		},
		weight: {
			normal: 'font-normal',
			medium: 'font-medium',
			semibold: 'font-semibold',
			bold: 'font-bold'
		},
		color: {
			black: 'color-normal',
			gray: 'text-free-gray-500'
		},
		font: {
			montserrat: 'font-montserrat',
			iliad: 'font-iliad',
			ibmplexmono: 'font-ibmplexmono'
		}
	},
	defaultVariants: {
		size: 'sm',
		weight: 'normal',
		color: 'black',
		font: 'montserrat'
	}
});

type TitleStyleProps = VariantProps<typeof TitleStyles>;

interface TitleProps extends React.HTMLAttributes<HTMLHeadingElement>, Omit<TitleStyleProps, 'level' | 'weight'> {
	variant: `${NonNullable<TitleStyleProps['level']>}/${NonNullable<TitleStyleProps['weight']>}`;
	color?: 'black' | 'gray';
	font?: 'montserrat' | 'iliad' | 'ibmplexmono';
}

const Title = forwardRef<HTMLHeadingElement, TitleProps>((props, ref) => {
	const { children, variant, className, color, font, ...rest } = props;

	const [level, weight] = variant.split('/') as [TitleStyleProps['level'], TitleStyleProps['weight']];

	const Comp: any = `h${level}`;

	return (
		<Comp ref={ref} className={cn(TitleStyles({ level, weight, color, font, className }))} {...rest}>
			{children}
		</Comp>
	);
});

type TextStyleProps = VariantProps<typeof TextStyles>;

interface TextProps extends React.HTMLAttributes<HTMLParagraphElement>, Omit<TextStyleProps, 'size' | 'weight'> {
	variant?: `${NonNullable<TextStyleProps['size']>}/${NonNullable<TextStyleProps['weight']>}`;
	as?: keyof Pick<JSX.IntrinsicElements, 'p' | 'code' | 'strong' | 'span' | 'small' | 'mark' | 'kbd'>;
	color?: 'black' | 'gray';
	font?: 'montserrat' | 'iliad' | 'ibmplexmono';
}

const Text = forwardRef<HTMLParagraphElement, TextProps>((props, ref) => {
	const { as: Tag = 'p', children, className, variant = 'sm/normal', color, font, ...rest } = props;

	const [size, weight] = variant?.split('/') as [TextStyleProps['size'], TextStyleProps['weight']];

	return (
		<Tag ref={ref} className={cn(TextStyles({ size, weight, color, font, className }))} {...rest}>
			{children}
		</Tag>
	);
});

Title.displayName = 'Title';
Text.displayName = 'Text';

const Typography = { Title, Text };

export default Typography;
