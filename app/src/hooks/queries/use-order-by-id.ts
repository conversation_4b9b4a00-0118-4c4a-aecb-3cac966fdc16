// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { isUndefined, last } from 'lodash';
// api
import type { IFiberCompanyContact } from '@/api/interface/fiber';
import type { IFiberCorpContact } from '@/api/interface/fiber';
import type { IFiberRetailerContact } from '@/api/interface/fiber';
import { queries } from '@/api/queries';

// ----------------------------------------------------------------------

export const useOrderById = (id: string) => {
	const queryOrder = useQuery({ ...queries.fiber_order.byId(id as string), enabled: Boolean(id) });
	const { data: order } = queryOrder;
	const { appointments, onus } = order || {};
	const { data: adel } = useQuery({
		...queries.crossroad_iw.byId(order?.endpointAddress?.iwId),
		enabled: !!order?.endpointAddress?.iwId
	});

	const oi = order?.ois?.find((oisItem) => isUndefined(oisItem.deletedAt) || last(order?.ois)?.['@id'] === oisItem['@id']);
	// TODO: appointment => appointmentLast
	const appointment = order?.appointments?.find(
		(appointmentsItem) => isUndefined(appointmentsItem?.deletedAt) || last(order?.appointments)?.['@id'] === appointmentsItem['@id']
	);

	const appointmentsActive = appointments?.filter((appointmentsItem) => isUndefined(appointmentsItem?.deletedAt));

	const linkState = order?.linkStates?.find(
		(linkStatesItem) => isUndefined(linkStatesItem.deletedAt) || last(order?.linkStates)?.['@id'] === linkStatesItem['@id']
	);
	const network = order?.networks?.find(
		(networksItem) => isUndefined(networksItem.deletedAt) || last(order?.networks)?.['@id'] === networksItem['@id']
	);
	const ip = network?.ips?.find((ipsItem) => isUndefined(ipsItem.deletedAt));

	const offerOrder = order?.offerOrders?.find(
		(offerOrdersItem) => isUndefined(offerOrdersItem.deletedAt) || last(order?.offerOrders)?.['@id'] === offerOrdersItem['@id']
	);
	const logisticOrderItems = order?.logisticOrderItems?.find(
		(logisticOrdersItem) =>
			isUndefined(logisticOrdersItem.deletedAt) || last(order?.logisticOrderItems)?.['@id'] === logisticOrdersItem['@id']
	);
	const rop = oi?.rops.find((ropsItem) => isUndefined(ropsItem.deletedAt) || last(oi?.rops)?.['@id'] === ropsItem['@id']);
	const onu = order?.onus?.find((ropsItem) => isUndefined(ropsItem.deletedAt) || last(order?.onus)?.['@id'] === ropsItem['@id']);
	const vlan = order?.vlans?.find((vlansItem) => isUndefined(vlansItem.deletedAt) || last(order.vlans)?.['@id'] === vlansItem['@id']);

	const companyContacts = order?.contacts?.filter(
		(contactItem) => isUndefined(contactItem?.deletedAt) && contactItem['@type'] === 'CompanyContact'
	) as IFiberCompanyContact[] | undefined;
	const corpContacts = order?.contacts?.filter(
		(contactItem) => isUndefined(contactItem?.deletedAt) && contactItem['@type'] === 'CorpContact'
	) as IFiberCorpContact[] | undefined;
	const retailerContacts = order?.contacts?.filter(
		(contactItem) => isUndefined(contactItem?.deletedAt) && contactItem['@type'] === 'RetailerContact'
	) as IFiberRetailerContact[] | undefined;

	return {
		adel,
		id,
		onus,
		order,
		linkState,
		network,
		offerOrder,
		vlan,
		oi,
		ip,
		logisticOrderItems,
		appointment,
		appointments,
		appointmentsActive,
		rop,
		onu,
		companyContacts,
		corpContacts,
		retailerContacts,
		queryOrder
	};
};
