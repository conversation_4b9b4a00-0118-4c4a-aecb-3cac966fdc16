import * as React from 'react';

// ----------------------------------------------------------------------

const MOBILE_BREAKPOINT = 768;

export function useIsMobile() {
	const [isMobile, setIsMobile] = React.useState<boolean>(window.innerWidth < MOBILE_BREAKPOINT);

	React.useEffect(() => {
		const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);
		const onChange = (e: MediaQueryListEvent) => {
			setIsMobile(e.matches);
		};
		mql.addEventListener('change', onChange);
		setIsMobile(mql.matches); // Initial check
		return () => mql.removeEventListener('change', onChange);
	}, []);

	return isMobile;
}
