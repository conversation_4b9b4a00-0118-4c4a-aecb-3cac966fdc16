// tanstack
import { createFileRoute } from '@tanstack/react-router';
// hoc
import { withErrorBoundarySentry } from '@/lib/hoc';
// locales
import { SiteMap } from '@/lib/i18n/sitemap';
// sections
import { ClientsOrdersView } from '@/sections/universe/clients/orders';
// components
import { CustomHelmet } from '@/components/custom';

// ----------------------------------------------------------------------

export const Route = createFileRoute('/_authenticated/(universe)/clients/orders')({
	component: withErrorBoundarySentry(ClientsOrdersPage)
});

function ClientsOrdersPage() {
	return (
		<>
			<CustomHelmet title={SiteMap.universe.clients.orders.title} description={SiteMap.universe.clients.orders.description} />

			<ClientsOrdersView />
		</>
	);
}
