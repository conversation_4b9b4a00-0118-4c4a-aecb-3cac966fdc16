// tanstack
import { createFileRoute } from '@tanstack/react-router';
// utils
import { z } from 'zod'; // Make sure you import zod
// hoc
import { withErrorBoundarySentry } from '@/lib/hoc';
// locales
import { SiteMap } from '@/lib/i18n/sitemap';
// sections
import { OrdersListView } from '@/sections/universe/fiber/orders';
// components
import { CustomHelmet } from '@/components/custom/custom-helmet';

// ----------------------------------------------------------------------

const orderTypeSchema = z.enum(['ftth', 'ftto']).optional().default('ftth');

export const Route = createFileRoute('/_authenticated/(universe)/fiber/orders/')({
	validateSearch: (search) => {
		return {
			type: orderTypeSchema.parse(search.type)
		};
	},
	component: withErrorBoundarySentry(FiberOrdersPage)
});

function FiberOrdersPage() {
	const { type } = Route.useSearch();

	return (
		<>
			<CustomHelmet title={type === 'ftto' ? SiteMap.universe.fiber.orders.ftto.title : SiteMap.universe.fiber.orders.ftth.title} />

			<OrdersListView type={type} />
		</>
	);
}
