// tanstack
import { createFileRoute } from '@tanstack/react-router';
// hoc
import { withErrorBoundarySentry } from '@/lib/hoc';
// locales
import { SiteMap } from '@/lib/i18n/sitemap';
// sections
import DashboardView from '@/sections/universe/dashboard-view';
// components
import { CustomHelmet } from '@/components/custom/custom-helmet';

// ----------------------------------------------------------------------

export const Route = createFileRoute('/_authenticated/dashboard')({
	component: withErrorBoundarySentry(DashboardPage)
});

function DashboardPage() {
	return (
		<>
			<CustomHelmet title={SiteMap.universe.dashboard.title} description={SiteMap.universe.dashboard.description} />

			<DashboardView />
		</>
	);
}
