import { useCallback, useMemo } from 'react';
// utils
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
// api
import type { IJobItem } from '@/api/interface';
import { JOB_TYPE_OPTIONS } from '@/api/enums/pawn';
import { errorsGeneric } from '@/api/fetcher';
import { useCreateJob, useUpdateJob } from '@/api/mutations';
import { type IJobSchema, JobSchema } from '@/api/schemas';
// hooks
import { toast } from '@/hooks/use-toast';
// components
import { SelectDropdown } from '@/components/select-dropdown';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Scroll<PERSON>rea } from '@/components/ui/scroll-area';

// ----------------------------------------------------------------------

interface Props {
	currentRow?: IJobItem;
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export function JobDialog({ currentRow, open, onOpenChange }: Props) {
	const createJob = useCreateJob();
	const updateJob = useUpdateJob({ jobIri: currentRow?.['@id'] || '' });

	const isEdit = !!currentRow;

	const defaultValues = useMemo(
		() => ({
			type: currentRow?.type || '',
			name: currentRow?.name || ''
		}),
		[currentRow]
	);
	const form = useForm<IJobSchema>({
		resolver: zodResolver(JobSchema),
		defaultValues
	});

	const onSubmit = useCallback(
		async (values: IJobSchema) => {
			try {
				if (!isEdit && !currentRow) {
					await createJob.mutate(values, {
						onSuccess: () => {
							toast({
								variant: 'success',
								title: 'Création du poste',
								description: `Le poste ${values.name} a bien été créé.`
							});

							onOpenChange(false);
						},
						onError: errorsGeneric
					});
				} else {
					await updateJob.mutate(values, {
						onSuccess: () => {
							toast({
								variant: 'success',
								title: 'Modification du poste',
								description: `Le poste ${currentRow?.name || ''} a bien été modifié.`
							});

							onOpenChange(false);
						},
						onError: errorsGeneric
					});
				}
			} catch (error: any) {
				toast({
					variant: 'error',
					title: 'Erreur',
					description: error.message
				});
			}
		},
		[currentRow, isEdit, onOpenChange]
	);

	return (
		<Dialog
			open={open}
			onOpenChange={(state) => {
				onOpenChange(state);
			}}>
			<DialogContent className='sm:max-w-lg'>
				<DialogHeader className='text-left'>
					<DialogTitle>{isEdit ? 'Modifier le poste' : 'Créer un nouveau poste'}</DialogTitle>
					<DialogDescription>
						{isEdit ? 'Modifier les informations du poste' : 'Créer un nouveau poste en remplissant les champs ci-dessous.'}
					</DialogDescription>
				</DialogHeader>

				<ScrollArea className='-mr-4 w-full py-1 pr-4'>
					<Form {...form}>
						<form id='job-form' onSubmit={form.handleSubmit(onSubmit)} className='space-y-4 p-0.5'>
							<FormField
								control={form.control}
								name='name'
								render={({ field }) => (
									<FormItem className='grid items-center grid-cols-6 space-y-0 gap-x-4 gap-y-1'>
										<FormLabel className='col-span-2 text-right'>Nom du poste</FormLabel>
										<FormControl>
											<Input placeholder='Développeur' className='col-span-4' autoComplete='off' {...field} />
										</FormControl>
										<FormMessage className='col-span-4 col-start-3' />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name='type'
								render={({ field }) => (
									<FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
										<FormLabel className='col-span-2 text-right'>Type de poste</FormLabel>
										<SelectDropdown
											defaultValue={field.value}
											onValueChange={(value) => field.onChange(value)}
											placeholder='Sélectionner un type de poste'
											className='col-span-4'
											items={JOB_TYPE_OPTIONS()}
										/>
										<FormMessage className='col-span-4 col-start-3' />
									</FormItem>
								)}
							/>
						</form>
					</Form>
				</ScrollArea>
				<DialogFooter>
					<Button variant='ghost' onClick={() => onOpenChange(false)}>
						Annuler
					</Button>

					<Button
						type='submit'
						form='job-form'
						//   loading={createJob.isLoading || updateJob.isLoading}
						disabled={!form.formState.isDirty}
					>
						{isEdit ? 'Modifier' : 'Créer'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
