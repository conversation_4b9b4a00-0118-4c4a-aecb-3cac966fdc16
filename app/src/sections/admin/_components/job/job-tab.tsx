import { useCallback, useState } from 'react';
// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { Loading } from '@tools/reactore';
// api
import type { IJobItem } from '@/api/interface';
import { parseIri } from '@/api/fetcher';
import { pawnQueryKeys } from '@/api/queries';
// sections
import { columns } from './table/columns';
// components
import Typography from '@/components/ui/typography';
import { DataTable } from '@/components/custom-table';

// ----------------------------------------------------------------------

const defaultFilters = {
	page: 1,
	itemsPerPage: 10,
	name: '',
	type: 'all'
};

// ----------------------------------------------------------------------

export function JobTab() {
	const [filters, setFilters] = useState(defaultFilters);

	const { data: jobsData, isLoading, isError } = useQuery(pawnQueryKeys.pawn_job.all(filters));

	const handleChangeFilters = useCallback((name: string, value: string) => {
		setFilters((prevFilters) => ({ ...prevFilters, [name]: value }));
	}, []);

	if (isError) {
		return (
			<div className='px-6 py-4 mt-6 space-y-4 rounded-3xl'>
				<Typography.Title variant='1/bold' className='text-free-state-error'>
					Une erreur interne est survenu veuillez réessayer plus tard
				</Typography.Title>
			</div>
		);
	}

	if (jobsData) {
		return (
			<DataTable
				linkId='jobs'
				columns={columns}
				data={jobsData?.['hydra:member'] || []}
				isLoading={isLoading}
				isSearchable
				searchValue={filters.name}
				onSearch={(value) => handleChangeFilters('name', value)}
				onReset={() => setFilters(defaultFilters)}
				getRowId={(row) => parseIri((row as IJobItem)['@id']) as string}
				totalRows={jobsData?.['hydra:totalItems']}
			/>
		);
	}

	return <Loading />;
}
