import { useCallback, useState } from 'react';
// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { Loading } from '@tools/reactore';
// api
import type { ISpaceItem } from '@/api/interface';
import { parseIri } from '@/api/fetcher';
import { magnetQueryKeys } from '@/api/queries';
// sections
import { columns } from './table/columns';
// components
import Typography from '@/components/ui/typography';
import { DataTable } from '@/components/custom-table';

// ----------------------------------------------------------------------

const defaultFilters = {
	page: 1,
	itemsPerPage: 10,
	name: ''
};

// ----------------------------------------------------------------------

export function SpaceTab() {
	const [filters, setFilters] = useState(defaultFilters);

	const { data: spacesData, isLoading, isError } = useQuery(magnetQueryKeys.magnet_space.all(filters));

	const handleChangeFilters = useCallback((name: string, value: string) => {
		setFilters((prevFilters) => ({ ...prevFilters, [name]: value }));
	}, []);

	if (isError) {
		return (
			<div className='px-6 py-4 mt-6 space-y-4 rounded-3xl'>
				<Typography.Title variant='1/bold' className='text-free-state-error'>
					Une erreur interne est survenu veuillez réessayer plus tard
				</Typography.Title>
			</div>
		);
	}

	if (spacesData) {
		return (
			<DataTable
				linkId='spaces'
				columns={columns}
				data={spacesData?.['hydra:member'] || []}
				isLoading={isLoading}
				isSearchable
				searchValue={filters.name}
				onSearch={(value) => handleChangeFilters('name', value)}
				onReset={() => setFilters(defaultFilters)}
				getRowId={(row) => parseIri((row as ISpaceItem)['@id']) as string}
				totalRows={spacesData?.['hydra:totalItems']}
			/>
		);
	}

	return <Loading />;
}
