import { useEffect, useState } from 'react';
// hooks
import { useMetricsAppointment } from '@/hooks/queries/use-metrics-appointments';
// sections
import LegendItem from './legend-item';
import { useAppointmentsSelectedLegends } from '../../../_hooks/atoms/use-appointments-selected-legends';
import { useAppointmentsSelectedMetrics } from '../../../_hooks/atoms/use-appointments-selected-metrics';
// others
import { metricColors } from '../graph-day-container/day-selector/bar-day/colors.utils';

// ----------------------------------------------------------------------

export const combineObject = (obj: object | undefined = {}, obj2: object | undefined = {}) => {
	const newObject = {};
	let i = 0;
	for (const [key] of Object.entries(obj ?? {})) {
		i += 1;
		newObject[key] = (obj[key] ?? 0) + (obj2[key] ?? 0);
	}
	return i ? newObject : undefined;
};

type TLegend = {
	label: string;
	color: string;
	total: number;
};

export default function AppointmentLegends() {
	const [selectedMetrics] = useAppointmentsSelectedMetrics();
	const { totalsByTime } = useMetricsAppointment();
	const [selectedLegend] = useAppointmentsSelectedLegends();

	const [doneLegends, setDoneLegends] = useState<TLegend[]>();
	const [plannedWithinBusinessHoursLegends, setPlannedWithinBusinessHoursLegends] = useState<TLegend[]>();
	const [plannedOutsideBusinessHoursLegends, setPlannedOutsideBusinessHoursLegends] = useState<TLegend[]>();

	useEffect(() => {
		setDoneLegends(
			Object.entries(metricColors.done[selectedMetrics.done]).map(([key, value]) => {
				return {
					color: value,
					label: key,
					total: totalsByTime?.done.withinBusinessHours[selectedMetrics.done]?.[key] || 0
				};
			})
		);
		setPlannedWithinBusinessHoursLegends(
			Object.entries(metricColors.planned[selectedMetrics.planned]).map(([key, value]) => {
				return {
					color: value.withinBusinessHours,
					label: key,
					total: totalsByTime?.planned.withinBusinessHours[selectedMetrics.planned]?.[key] || 0
				};
			})
		);
		setPlannedOutsideBusinessHoursLegends(
			Object.entries(metricColors.planned[selectedMetrics.planned]).map(([key, value]) => {
				return {
					color: value.outsideBusinessHours,
					label: key,
					total: totalsByTime?.planned.outsideBusinessHours[selectedMetrics.planned]?.[key] || 0
				};
			})
		);
	}, [selectedMetrics, totalsByTime]);

	return (
		<div className='flex items-start justify-between w-full h-full mb-5'>
			<div className='flex flex-1 space-x-3'>
				<div className='flex flex-wrap-reverse child:mb-1 child:w-14'>
					{doneLegends?.map((item) => (
						<LegendItem color={item.color} state={item.label} total={item.total} />
					))}
					<div className='flex items-center space-x-1 text-xs font-semibold'>
						<div className='flex items-center justify-center rounded-xl bg-gray-300 px-2 py-[2px]'>
							{selectedLegend.done
								? selectedLegend.done.reduce((acc, curr) => {
										const currSelectedMetricsTotal =
											totalsByTime?.done.outsideBusinessHours[selectedMetrics.done]?.[curr];

										return currSelectedMetricsTotal ? acc + currSelectedMetricsTotal : acc;
									}, 0)
								: totalsByTime?.done.outsideBusinessHours[selectedMetrics.done]?.total || 0}
						</div>
						<div className='text-center dark:text-gray-200'>HNO</div>
					</div>
				</div>
			</div>
			<div className='flex flex-col items-end flex-1 space-y-2'>
				<div className='flex flex-wrap-reverse items-center justify-end child:mb-1 child:w-14'>
					<div className='text-xs font-medium text-center font-montserrat dark:text-gray-200'>HNO</div>
					{plannedOutsideBusinessHoursLegends?.map((item) => (
						<LegendItem variant='planned' color={item.color} state={item.label} total={item.total} isOutsideBusinessHours />
					))}
				</div>
				<div className='flex flex-wrap-reverse items-center justify-end child:mb-1 child:w-14'>
					{plannedWithinBusinessHoursLegends?.map((item) => (
						<LegendItem variant='planned' color={item.color} state={item.label} total={item.total} />
					))}
				</div>
			</div>
		</div>
	);
}
