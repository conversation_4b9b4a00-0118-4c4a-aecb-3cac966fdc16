// const inputUpr: [string, ...string[]] = [
// 	'Default',
// 	'00',
// 	'01',
// 	'02',
// 	'03',
// 	'04',
// 	'05',
// 	'06',
// 	'07',
// 	'08',
// 	'09',
// 	'10',
// 	'11',
// 	'12',
// 	'13',
// 	'14',
// 	'15',
// 	'16',
// 	'17'
// ]

export default function FilterBar() {
	// const [isOpen, setIsOpen] = useState(false)
	// const { multiSelect, dispatchFilter, upr } = useFilterColumn()
	//
	// const handleChangeMult = (el: TOptionListSelect | undefined, value?: boolean) => {
	// 	if (el) dispatchFilter({ type: 'sortMultiSelect', action: el })
	// 	else if (typeof value === 'boolean') dispatchFilter({ type: 'sortMultiSelectAll', action: value })
	// }
	//
	// const handleChangeUpr = (value: string) => {
	// 	dispatchFilter({ type: 'sortUpr', action: value })
	// }
	//
	// return (
	// 	<div className='flex w-full flex-col items-center justify-center'>
	// 		<div className='mb-2 flex w-full justify-between rounded-lg '>
	// 			<div className='' />
	// 			<div
	// 				className='mr-2 flex cursor-pointer items-center justify-center'
	// 				onClick={() => setIsOpen((el) => !el)}
	// 				aria-hidden='true'
	// 			>
	// 				<FilterIcon className='w-5 text-gray-500' />
	// 				<div className='font-medium text-gray-600'>Filtre</div>
	// 			</div>
	// 		</div>
	// 		<div
	// 			className='w-full transition-all duration-200'
	// 			style={{
	// 				height: isOpen ? '80px' : '0px',
	// 				overflowY: !isOpen ? 'auto' : 'visible'
	// 			}}
	// 		>
	// 			<div className='flex w-full items-start justify-around'>
	// 				<div className=''>
	// 					<Input
	// 						type='select'
	// 						label='UPR'
	// 						name='filter'
	// 						selectOptions={{ items: inputUpr }}
	// 						onChangeList={handleChangeUpr}
	// 						defaultValue={upr}
	// 						size='sm'
	// 					/>
	// 				</div>
	// 				<div className='w-[70%]'>
	// 					<InputSelectCustom
	// 						// label='Type de rendez vous' TODO: a revoir
	// 						optionList={multiSelect}
	// 						handleChange={handleChangeMult}
	// 						placeholder='Label'
	// 						// noWrap TODO: a revoir
	// 					/>
	// 				</div>
	// 			</div>
	// 		</div>
	// 	</div>
	// )
}
