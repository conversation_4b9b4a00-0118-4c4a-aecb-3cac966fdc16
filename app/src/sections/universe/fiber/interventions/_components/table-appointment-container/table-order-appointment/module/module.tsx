// tanstack
import { Link } from '@tanstack/react-router';
// utils
import { format, parse } from 'date-fns';
// components
import { BanFillCircleIcon, CheckFillCircleIcon, PacmanIcon, RefreshCircleIcon, VectorIcon } from '@/components-old/ui/icon';

// ----------------------------------------------------------------------

const icons = {
	free: <VectorIcon className='text-free-red-300' />,
	jag: <PacmanIcon className='text-blue-400' />
};

const strucString = (str: string) => {
	return str.split('_').join(' ') || str;
};

export function TypeR({ str, migrate, className }: { str: string; className: string; migrate: boolean }) {
	const Icons = icons.jag;
	return (
		<div className={`flex font-['Montserrat'] text-base ${className}`}>
			<div className='flex w-72 min-w-69 flex-1'>
				<div className='mr-4 h-6 w-6 rounded-full bg-slate-100 p-1'>{Icons}</div>
				<div className='truncate capitalize'>{strucString(str)}</div>
			</div>
			<div className='flex flex-1 justify-center'>{!migrate || <RefreshCircleIcon className='text-slate-500' />}</div>
		</div>
	);
}

export function HoursR({ str, className }: { str: Date | string; className: string }) {
	let color = '';

	if (str !== '') color = 'text-slate-500 font-normal';
	else color = 'text-[#F97316] font-semibold';
	const hrs =
		str !== ''
			? `${format(parse(str.toString(), 'HH:mm', new Date()), 'kk')}h${format(parse(str.toString(), 'HH:mm', new Date()), 'mm')}`
			: 'N/A';

	return (
		<div className={`flex items-center ${className}`}>
			<div className={`font-['Montserrat'] text-[14px]  text-xs leading-[22.4px] ${color}`}>{hrs}</div>
		</div>
	);
}

export function HoursD({ str, className }: { str: string; className: string }) {
	let color = '';

	if (str !== '') color = 'text-slate-500 font-normal';
	else color = 'text-[#F97316] font-semibold';
	const hrs = str !== '' ? str : 'N/A';
	return (
		<div className={`flex items-center ${className}`}>
			<div className={`font-['Montserrat'] text-[14px]  text-xs leading-[22.4px] ${color}`}>{hrs}</div>
		</div>
	);
}

export function State({
	str = 'FULL',
	nb = 1,
	state = 'CREATED',
	className
}: {
	str?: string;
	nb?: number | undefined;
	state?: string;
	className?: string;
}) {
	let color = '';
	let colorMore = '';
	let textColor = '';

	if (state === 'CREATED') {
		colorMore = 'bg-green-200';
		color = 'bg-green-100';
		textColor = 'text-green-600';
	} else if (state === 'medium') {
		colorMore = 'bg-[#FEF3C7]';
		color = 'bg-[#FEF3C7]/50';
		textColor = 'text-green-600';
	} else if (state === 'error') {
		colorMore = 'bg-red-200';
		color = 'bg-red-100';
		textColor = 'text-red-600';
	} else if (state === 'venir') {
		colorMore = 'bg-slate-200';
		color = 'bg-slate-100';
		textColor = '';
	}

	return (
		<div className={`flex items-center ${className}`}>
			<div className='flex flex-1'>
				<div className={`p-2 py-1 ${nb ? 'rounded-l-3xl' : 'rounded-3xl'} w-min text-xs font-semibold ${textColor} ${color}`}>
					{str}
				</div>
				{!!nb && (
					<div className={`rounded-r-3xl font-semibold ${color}`}>
						<div className={`w-min rounded-l-md rounded-r-3xl p-2 py-1 text-xs font-semibold ${textColor} ${colorMore}`}>
							{nb}
						</div>
					</div>
				)}
			</div>
			<div className='flex flex-1 justify-center'>
				{state === 'CREATED' || state === 'venir' ? (
					<div className='w-5'>
						<CheckFillCircleIcon className='text-green-600' />
					</div>
				) : (
					<BanFillCircleIcon className='text-red-600' />
				)}
			</div>
		</div>
	);
}
export function Order({ str, className }: { str: string; className: string }) {
	function StringInLineColor({ str = '' }: { str: string }): React.ReactNode {
		const tab = str.split('');

		function AlgoChart({ char }: { char: string }): React.ReactNode {
			const tmp = Math.floor(Number(char));
			const bgColor: [string, string] = ['text-blue-600', 'text-red-600'];
			const colorChar = (): string => {
				if (!Number.isNaN(tmp) && typeof tmp === 'number') {
					if (tmp === 0) return bgColor[1];
					return '';
				}
				return bgColor[0];
			};

			return <div className={`font-IBM text-4 leading-7 ${colorChar()}`}>{char}</div>;
		}

		return (
			<Link to={`/fiber/orders/${str}`} legacyBehavior>
				<button className='flex w-full'>
					{tab.map((char: string, ind: number) => {
						return <AlgoChart key={`key${ind}`} char={char} />;
					})}
				</button>
			</Link>
		);
	}

	return (
		<div className={`flex items-center justify-start ${className}`}>
			<StringInLineColor str={str} />
		</div>
	);
}
export function Maj({ str, className }: { str: string; className: string }) {
	return (
		<div className={`flex items-center justify-start ${className}`}>
			<div className='overflow-hidden text-ellipsis whitespace-nowrap pr-2'>{str}</div>
		</div>
	);
}
export function Upr({ str }: { str: string }) {
	const tab = str.split('');

	function AlgoChart({ char }: { char: string }): React.ReactNode {
		const tmp = Math.floor(Number(char));

		const colorChar = (): string => {
			if (!Number.isNaN(tmp) && typeof tmp === 'number') return 'font-semibold ';
			return 'font-light text-slate-400';
		};

		return <div className={`pr-[1px] font-['Montserrat'] ${colorChar()}`}>{char}</div>;
	}

	return (
		<div className='flex h-[20px] items-center justify-start pr-10'>
			{tab.map((char: string, ind: number) => {
				return <AlgoChart key={`key${ind}`} char={char} />;
			})}
		</div>
	);
}
