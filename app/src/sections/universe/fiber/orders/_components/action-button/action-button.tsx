import { useRef, useState } from 'react';
// utils
import { useOnClickOutside } from '@tools/reactore';
import { twJoin } from 'tailwind-merge';
// components
import { CrossIcon, FlashlightIcon } from '@/components-old/ui/icon';
// others
import ActionMenu from './ActionMenu';

// ----------------------------------------------------------------------

export default function ActionButton() {
	const divRef = useRef<HTMLDivElement>(null);

	const [menuOpen, setMenuOpen] = useState(false);
	useOnClickOutside(divRef, () => {
		setMenuOpen(false);
	});

	return (
		<div className='fixed bottom-4 right-8 z-20'>
			<div ref={divRef} className='flex flex-col items-end'>
				<ActionMenu open={menuOpen} setOpen={setMenuOpen} />
				<button onClick={() => setMenuOpen((prev) => !prev)} className='rounded-2xl bg-white p-3 shadow-lg dark:bg-free-black'>
					{menuOpen ? <CrossIcon className={twJoin('h-8 w-8 text-free-gray-300')} /> : <FlashlightIcon className='h-8 w-8' />}
				</button>
			</div>
		</div>
	);
}
