// utils
import { formatDate, Typography } from '@tools/reactore';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import NotAvailable from '@/components-old/ui/not-available';
import { ClientOfferBubbleIcon, OfferShapeIcon } from '@/components-old/ui/icon';

// ----------------------------------------------------------------------

function ClientOffer() {
	const { order, offerOrder } = useOrder();

	const offerOrderOther = order?.products;

	if (offerOrder?.offer['@id'] === '/offers/2') return null;

	if (order) {
		return (
			<section className='col-span-1'>
				<div className='flex h-full flex-col'>
					<div className='mb-2 flex items-end'>
						<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
							Offre du client
						</Typography>
					</div>
					<div className='card relative h-full overflow-hidden rounded-2xl p-10'>
						<OfferShapeIcon className='absolute left-0 top-0 z-10 text-[#EFF6FF] dark:text-free-gray-700' />
						<ClientOfferBubbleIcon className='absolute -left-2 -top-2 z-0 h-3' />
						<div className='relative z-10 border-b pb-5'>
							<div className='flex'>
								<img src='/assets/images/fiber/order/freebox_pro.png' alt='freebox pro' />
								<div className='ml-12'>
									<Typography type='h3' className='text-lg font-semibold'>
										{offerOrder?.offer?.name}
									</Typography>
									{offerOrder?.createdAt && (
										<Typography className='text-sm' color='gray'>
											Depuis le {formatDate(offerOrder?.createdAt, 'dd/MM/yyyy')}
										</Typography>
									)}
								</div>
							</div>
						</div>
						<div className='relative z-10 pt-5'>
							<Typography type='h4' fontFamily='montserrat' className='text-base font-semibold'>
								Autres services
							</Typography>
							{offerOrderOther?.length ? (
								<div className='my-6 grid h-3 grid-cols-2 grid-rows-2 gap-6'>
									{offerOrderOther?.map((product) => {
										// const iconName = productsWithIcon.find(
										// 	(item) => item.id === product?.product?.['@id']
										// )?.icon

										return (
											<Typography key={product['@id']} className='col-span-1 flex items-center'>
												{/* {iconName && <Icon name={iconName} className='mr-2 h-5' />} */}
												{product?.name}
											</Typography>
										);
									})}
								</div>
							) : (
								<div className='mt-2'>
									<NotAvailable />
								</div>
							)}
						</div>
					</div>
				</div>
			</section>
		);
	}

	return (
		<section className='col-span-1'>
			<div className='flex h-full flex-col'>
				<div className='mb-2 flex items-end'>
					<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
						Offre du client
					</Typography>
				</div>
				<Skeleton className='h-116 rounded-2xl' />
			</div>
		</section>
	);
}
export default ClientOffer;
