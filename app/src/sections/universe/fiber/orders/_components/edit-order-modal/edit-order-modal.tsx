import { useState } from 'react';
// tanstack
import { type InvalidateQueryFilters, useMutation, useQueryClient } from '@tanstack/react-query';
// utils
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
// components
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/hook-form/form-provider';
// sections
import ReferentielIdentificationTab from './tabs/referentiel-identification-tab';
import ReferentielGeographiqueTab from './tabs/referentiel-geographique-tab';
import ReferentielOptiqueTab from './tabs/referentiel-optique-tab';
import OffreCommercialeTab from './tabs/offre-commerciale-tab';
import AutreTab from './tabs/autre-tab';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
import { mutationFn } from '@/hooks/use-mutation';
// types
import type { EditOrderModalProps } from './types';
import { editOrderSchema, type EditOrderFormData } from './schema';

// ----------------------------------------------------------------------

export default function EditOrderModal({ isOpen, onClose }: EditOrderModalProps) {
	const [activeTab, setActiveTab] = useState('referentiel-identification');
	const queryClient = useQueryClient();
	const { order } = useOrder();
	console.log('EditOrderModal order:', order);
	const methods = useForm<EditOrderFormData>({
		resolver: zodResolver(editOrderSchema),
		defaultValues: {
			// Tab 1: Référentiel identification
			jetonCommande: order?.reference || 'reference',
			entrepriseCliente: '',
			retailer: '',
			referenceBDC: '',
			referenceEP: '',
			referenceJFP: '',
			entrepriseDistributeur: '',

			// Tab 2: Référentiel géographique
			iwAdresseId: '',
			latitude: '',
			longitude: '',
			complementAdresse: '',
			batimentIMB: '',
			referenceAPCBatiment: '',
			referenceAPCEscalier: '',
			referenceAPCEtage: '',

			// Tab 3: Référentiel optique
			commandeMultiAcces: false,
			autoriserMigrationB2CB2B: false,
			pm: '',
			ptoARaccorder: '',
			ptoAConserver: '',
			referenceClientB2C: '',
			pmt: '',

			// Tab 4: Offre commerciale optique
			provenance: '',
			venteIndirecteCanal: '',
			garantiesSAV: '',
			typeOffre: '',
			freeboxPro1: false,
			freeboxPro2: false,
			kitMiseEnBaie1U: false,
			comsProUCaaS: false,
			supportPremium: false,
			disqueDurNVMe1To: false,
			backup4G: false,
			repeteurWIFI: false,
			repeteurWIFIQuantity: 0,

			// Tab 5: Autre
			commandeTest: false,
			modeAutomatique: false,
			vip: false
		}
	});

	const { handleSubmit, reset } = methods;

	const updateOrderMutation = useMutation({
		mutationFn: (data: EditOrderFormData) => mutationFn('PUT', { url: `${order?.['@id']}`, data, api: 'fiber' }),
		onSuccess: () => {
			queryClient.invalidateQueries(['orders', 'byId'] as InvalidateQueryFilters<readonly unknown[]>);
			onClose();
			reset();
		}
	});

	const onSubmit = (data: EditOrderFormData) => {
		updateOrderMutation.mutate(data);
	};

	const handleClose = () => {
		reset();
		setActiveTab('referentiel-identification');
		onClose();
	};

	return (
		<Dialog open={isOpen} onOpenChange={handleClose}>
			<DialogContent className='max-w-4xl h-[635px] overflow-hidden flex flex-col bg-[#FBFCFE] dark:bg-[#141414]'>
				<DialogHeader>
					<DialogTitle>Modifier les paramètres de commande</DialogTitle>
				</DialogHeader>

				<Form methods={methods} onSubmit={handleSubmit(onSubmit)} className='flex-1 overflow-hidden'>
					<Tabs value={activeTab} onValueChange={setActiveTab} className='flex-1 flex flex-col'>
						<TabsList variant='underline'>
							<TabsTrigger variant='underline' value='referentiel-identification'>
								Référentiel identification
							</TabsTrigger>
							<TabsTrigger variant='underline' value='referentiel-geographique'>
								Référentiel géographique
							</TabsTrigger>
							<TabsTrigger variant='underline' value='referentiel-optique'>
								Référentiel optique
							</TabsTrigger>
							<TabsTrigger variant='underline' value='offre-commerciale'>
								Offre commerciale optique
							</TabsTrigger>
							<TabsTrigger variant='underline' value='autre'>
								Autre
							</TabsTrigger>
						</TabsList>

						<div className='overflow-y-scroll mt-6 p-0.5'>
							<TabsContent value='referentiel-identification' className='mt-0'>
								<ReferentielIdentificationTab />
							</TabsContent>

							<TabsContent value='referentiel-geographique' className='mt-0'>
								<ReferentielGeographiqueTab />
							</TabsContent>

							<TabsContent value='referentiel-optique' className='mt-0'>
								<ReferentielOptiqueTab />
							</TabsContent>

							<TabsContent value='offre-commerciale' className='mt-0'>
								<OffreCommercialeTab />
							</TabsContent>

							<TabsContent value='autre' className='mt-0'>
								<AutreTab />
							</TabsContent>
						</div>
					</Tabs>
				</Form>

				<DialogFooter className='flex justify-end space-x-4'>
					<Button variant='outline' onClick={handleClose}>
						Annuler
					</Button>
					<Button type='submit' onClick={handleSubmit(onSubmit)} disabled={updateOrderMutation.isPending}>
						{updateOrderMutation.isPending ? 'Enregistrement...' : 'Enregistrer'}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
