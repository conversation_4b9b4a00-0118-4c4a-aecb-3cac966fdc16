// utils
import { z } from 'zod';
// locales
import { REQUIRED_FIELD } from '@/lib/i18n/constante.i18n';

// ----------------------------------------------------------------------

export const editOrderSchema = z.object({
  // Tab 1: Référentiel identification
  jetonCommande: z.string().min(1, REQUIRED_FIELD),
  entrepriseCliente: z.string().min(1, REQUIRED_FIELD),
  retailer: z.string().min(1, REQUIRED_FIELD),
  referenceBDC: z.string().optional(),
  referenceEP: z.string().optional(),
  referenceJFP: z.string().optional(),
  entrepriseDistributeur: z.string().optional(),

  // Tab 2: Référentiel géographique
  iwAdresseId: z.string().min(1, REQUIRED_FIELD),
  latitude: z.string().optional(),
  longitude: z.string().optional(),
  complementAdresse: z.string().optional(),
  batimentIMB: z.string().optional(),
  referenceAPCBatiment: z.string().optional(),
  referenceAPCEscalier: z.string().optional(),
  referenceAPCEtage: z.string().optional(),

  // Tab 3: Référentiel optique
  commandeMultiAcces: z.boolean().default(false),
  autoriserMigrationB2CB2B: z.boolean().default(false),
  pm: z.string().optional(),
  ptoARaccorder: z.string().optional(),
  ptoAConserver: z.string().min(1, REQUIRED_FIELD),
  referenceClientB2C: z.string().optional(),
  pmt: z.string().optional(),

  // Tab 4: Offre commerciale optique
  provenance: z.string().optional(),
  venteIndirecteCanal: z.string().optional(),
  garantiesSAV: z.string().optional(),
  typeOffre: z.string().optional(),
  // Options
  freeboxPro1: z.boolean().default(false),
  freeboxPro2: z.boolean().default(false),
  kitMiseEnBaie1U: z.boolean().default(false),
  comsProUCaaS: z.boolean().default(false),
  supportPremium: z.boolean().default(false),
  disqueDurNVMe1To: z.boolean().default(false),
  backup4G: z.boolean().default(false),
  repeteurWIFI: z.boolean().default(false),
  repeteurWIFIQuantity: z.number().min(0).default(0),

  // Tab 5: Autre
  commandeTest: z.boolean().default(false),
  modeAutomatique: z.boolean().default(false),
  vip: z.boolean().default(false),
});

export type EditOrderFormData = z.infer<typeof editOrderSchema>;
