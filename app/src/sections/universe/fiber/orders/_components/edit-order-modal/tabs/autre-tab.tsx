// utils
import { useFormContext } from 'react-hook-form';
// components
import { SwitchCard } from '@/components/ui/switch-card';
// types
import type { EditOrderFormData } from '../schema';

// ----------------------------------------------------------------------

export default function AutreTab() {
	const { control, watch } = useFormContext<EditOrderFormData>();

	const commandeTest = watch('commandeTest');
	const modeAutomatique = watch('modeAutomatique');
	const vip = watch('vip');

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-2 gap-4'>
				<div className='space-y-4'>
					<FormField
						control={control}
						name='commandeTest'
						render={({ field }) => (
							<FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
								<div className='flex justify-between items-center w-full'>
									<div className='flex gap-3 items-center '>
										<FormControl>
											<Switch checked={field.value} onCheckedChange={field.onChange} />
										</FormControl>
										<FormLabel className='text-base'>Commande de test</FormLabel>
									</div>
									<Badge variant={commandeTest ? 'green' : 'gray'} size='sm'>
										{commandeTest ? 'Actif' : 'Inactif'}
									</Badge>
								</div>
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='modeAutomatique'
						render={({ field }) => (
							<FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
								<div className='flex justify-between items-center w-full'>
									<div className='flex gap-3 items-center '>
										<FormControl>
											<Switch checked={field.value} onCheckedChange={field.onChange} />
										</FormControl>
										<FormLabel className='text-base'>Mode automatique</FormLabel>
									</div>
									<Badge variant={modeAutomatique ? 'green' : 'gray'} size='sm'>
										{modeAutomatique ? 'Actif' : 'Inactif'}
									</Badge>
								</div>
							</FormItem>
						)}
					/>
				</div>

				<div className='space-y-4'>
					<FormField
						control={control}
						name='vip'
						render={({ field }) => (
							<FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
								<div className='flex justify-between items-center w-full'>
									<div className='flex gap-3 items-center '>
										<FormControl>
											<Switch checked={field.value} onCheckedChange={field.onChange} />
										</FormControl>
										<FormLabel className='text-base'>VIP</FormLabel>
									</div>
									<Badge variant={vip ? 'green' : 'gray'} size='sm'>
										{vip ? 'Actif' : 'Inactif'}
									</Badge>
								</div>
							</FormItem>
						)}
					/>
				</div>
			</div>
		</div>
	);
}
