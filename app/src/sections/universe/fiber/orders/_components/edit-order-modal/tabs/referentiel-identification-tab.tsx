// utils
import { useFormContext } from 'react-hook-form';
// components
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { FormLabelRequired } from '@/components/ui/form-label-required';
import { Input } from '@/components/ui/input';
// types
import type { EditOrderFormData } from '../schema';

// ----------------------------------------------------------------------

export default function ReferentielIdentificationTab() {
	const { control } = useFormContext<EditOrderFormData>();

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-2 gap-4'>
				{/* Column 1 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='jetonCommande'
						render={({ field }) => (
							<FormItem>
								<FormLabelRequired required>Jeton de commande</FormLabelRequired>
								<FormControl>
									<Input placeholder='Jeton de commande' className={'bg-card'} {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='entrepriseCliente'
						render={({ field }) => (
							<FormItem>
								<FormLabelRequired required>Entreprise cliente</FormLabelRequired>
								<FormControl>
									<Input placeholder='Entreprise cliente' className={'bg-card'} {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='retailer'
						render={({ field }) => (
							<FormItem>
								<FormLabelRequired required>Retailer</FormLabelRequired>
								<FormControl>
									<Input placeholder='Retailer' className={'bg-card'} {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				{/* Column 2 */}
				<div className='space-y-4'>
					<FormField
						control={control}
						name='referenceBDC'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence BDC</FormLabel>
								<FormControl>
									<Input placeholder='Référence BDC' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceEP'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence EP</FormLabel>
								<FormControl>
									<Input placeholder='Référence EP' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<FormField
						control={control}
						name='referenceJFP'
						render={({ field }) => (
							<FormItem>
								<FormLabel>Référence JFP</FormLabel>
								<FormControl>
									<Input placeholder='Référence JFP' {...field} className={'bg-card'} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>
			</div>

			{/* Full-width input */}
			<FormField
				control={control}
				name='entrepriseDistributeur'
				render={({ field }) => (
					<FormItem>
						<FormLabel>Si Wholesale : Entreprise distributeur</FormLabel>
						<FormControl>
							<Input placeholder='Entreprise distributeur' {...field} className={'bg-card'} />
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</div>
	);
}
