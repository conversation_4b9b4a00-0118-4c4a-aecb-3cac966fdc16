import { useMemo } from 'react';
// tanstack
import { useQuery } from '@tanstack/react-query';
import { useParams } from '@tanstack/react-router';
// utils
import { getColorStateOrder } from '@/utils/common.utils';
import { Button, formatDate, parseIri, priceUtil, Typography } from '@tools/reactore';
import { sum } from 'lodash';
import { twJoin } from 'tailwind-merge';
// api
import { fiberFetcher } from '@/api/fetcher';
import { EFiberInvoiceState } from '@/api/interface/fiber';
import { queries } from '@/api/queries';
// hooks
import useMutation from '@/hooks/use-mutation';
import { useOrder } from '@/hooks/queries/use-order';
// components
import Skeleton from '@/components-old/reactore/tou-migrate/skeleton';
import Tag from '@/components-old/reactore/tou-migrate/tag';
import NotAvailable from '@/components-old/ui/not-available';
import { EyeIcon } from '@/components-old/ui/icon';

// ----------------------------------------------------------------------

export default function Invoices() {
	const { order } = useOrder();
	const { id } = useParams({ strict: false });

	const { data } = useQuery({
		...queries.fiber_order.byId(id as string)._ctx.invoices,
		select: (invoicesRep) => {
			if ('hydra:member' in invoicesRep) {
				return invoicesRep?.['hydra:member'];
			}
			return invoicesRep;
		},
		enabled: Boolean(id)
	});

	const getInvoicePdf = useMutation<Blob>({
		mutationFn: (data) =>
			fiberFetcher({
				url: `${data}/pdf`,
				config: {
					responseType: 'blob',
					headers: {
						'Content-Type': 'application/pdf'
						// Accept: 'application/pdf'
					}
				}
			})
	});

	const invoices = order?.invoices
		?.filter((invoice) => !invoice.deletedAt)
		?.sort((a, b) => {
			if (a.createdAt && b.createdAt) {
				if (a.createdAt > b.createdAt) {
					return -1;
				}
				if (a.createdAt < b.createdAt) {
					return 1;
				}
			}

			return 0;
		});
	const sumInvoices = useMemo(() => {
		return invoices?.length ? (
			priceUtil.format(sum([...invoices.map((invoice) => invoice.opex), ...invoices.map((invoice) => invoice.capex)]), {
				hasCentime: true,
				hasCurrency: true
			})
		) : (
			<NotAvailable />
		);
	}, [invoices?.length]);

	if (order) {
		return (
			<section className='col-span-1'>
				<div className='flex h-full flex-col'>
					<div className='mb-2 flex items-end'>
						<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
							Facturation interne
						</Typography>
					</div>
					<div className='card flex h-full max-h-85.752 flex-col justify-between overflow-auto rounded-2xl p-6'>
						{invoices && invoices?.length ? (
							<>
								<Typography className='text-right'>
									Total : <span className='body-xl font-semibold text-free-red'>{sumInvoices}</span>
								</Typography>
								{invoices.map((invoice) => {
									return (
										<article className='card mt-2 rounded-lg border border-gray-100 p-4 dark:bg-free-gray-800 dark:text-white'>
											<section className='mb-4 flex justify-between'>
												<div>
													<Tag className='badge-gray text-gray-700'>Devis</Tag>
													<span className='ml-2 text-sm font-semibold'>#{parseIri(invoice['@id'])}</span>
												</div>
												{invoice?.state && (
													<Tag className={twJoin(getColorStateOrder(invoice.state))}>
														{EFiberInvoiceState[invoice.state]}
													</Tag>
												)}
											</section>
											<section className='mb-2 flex justify-start'>
												<div className='flex w-full items-center gap-x-2'>
													{invoice?.createdAt && (
														<div className='dark:text-whit mr-4 flex flex-col items-center rounded-lg border border-free-gray-100 bg-free-white p-2 dark:bg-free-gray-800'>
															<Typography className='font-semibold'>
																{formatDate(invoice?.createdAt, 'dd MMM')}
															</Typography>
															<Typography type='caption' color='gray' className='dark:text-white'>
																{formatDate(invoice?.createdAt, 'yyyy')}
															</Typography>
														</div>
													)}
													<div>
														<Typography type='caption' color='gray'>
															Référence du document
														</Typography>
														<Typography className='font-semibold'>{invoice.reference}</Typography>
													</div>
													<div className='flex flex-1 justify-end'>
														<Button
															className='p-0'
															onClick={() => {
																getInvoicePdf.mutateAsync(invoice['@id']).then((invoicePdFRe) => {
																	window.open(window.URL.createObjectURL(invoicePdFRe));
																});
															}}>
															<EyeIcon className='h-5 text-free-gray-500 dark:text-white' />
														</Button>
													</div>
												</div>
											</section>
											<section className='mt-4 flex justify-between rounded-lg bg-white p-2 dark:bg-free-gray-800 dark:text-white'>
												<Typography className='text-xs'>
													Variable:{' '}
													<span className='text-lg font-bold'>
														{invoice.opex
															? priceUtil.format(invoice?.opex, {
																	hasCentime: true,
																	hasCurrency: true
																})
															: 'N/A'}
													</span>
												</Typography>
												<Typography className='text-xs'>
													Fixe:{' '}
													<span className='text-lg font-bold'>
														{invoice.capex
															? priceUtil.format(invoice?.capex, {
																	hasCentime: true,
																	hasCurrency: true
																})
															: 'N/A'}
													</span>
												</Typography>
											</section>
										</article>
									);
								})}
							</>
						) : (
							<NotAvailable />
						)}
					</div>
				</div>
			</section>
		);
	}

	return (
		<section className='col-span-1'>
			<div className='flex h-full flex-col'>
				<div className='mb-2 flex items-end'>
					<Typography type='h5' fontFamily='iliad' className='mr-6 text-xl font-normal text-gray-400 dark:text-white'>
						Facturation interne
					</Typography>
				</div>
				<Skeleton className='card h-125 rounded-2xl' />
			</div>
		</section>
	);
}
