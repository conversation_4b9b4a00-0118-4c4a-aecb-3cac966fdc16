// utils
import { <PERSON><PERSON>, <PERSON><PERSON>Header } from '@tools/reactore';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// types
import type { TBaseModalProps } from '@/types/modal.interface';

// ----------------------------------------------------------------------

export default function IpHistoryModal({ isOpen, onClose }: TBaseModalProps) {
	const { vlan } = useOrder();

	return (
		<Modal isOpen={isOpen} onClose={onClose}>
			<ModalHeader title='Historique IP' hasCloseButton />
		</Modal>
	);
}
