// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { Modal, ModalBody, ModalHeader } from '@tools/reactore';
// api
import { queries } from '@/api/queries';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// types
import type { TBaseModalProps } from '@/types/modal.interface';

// ----------------------------------------------------------------------

export default function IpLiveModal({ isOpen, onClose }: TBaseModalProps) {
	const { order } = useOrder();

	const { data } = useQuery({ ...queries.fiber_network.byId(order?.currentNetworks?.['@id'])._ctx.ipsCurrent });

	return (
		<Modal isOpen={isOpen} onClose={onClose}>
			<ModalHeader title='Récupération IP live' hasCloseButton />
			<ModalBody>{JSON.stringify(data)}</ModalBody>
		</Modal>
	);
}
