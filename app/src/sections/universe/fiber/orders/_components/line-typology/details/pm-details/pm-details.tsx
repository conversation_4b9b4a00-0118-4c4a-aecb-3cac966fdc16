// utils
import { Tooltip, Typography } from '@tools/reactore';
// api
import { EPMType } from '@/api/interface/fiber';
// hooks
import { useOrder } from '@/hooks/queries/use-order';
// components
import Tag from '@/components-old/reactore/tou-migrate/tag';

// ----------------------------------------------------------------------

function PmDetails() {
	const { rop, order } = useOrder();

	const pm = order?.currentRops?.pm;

	// Afficher pmt (currentRops.pm.technicalName), exploitant (currentOis), nom (currentOis pm name), coupleur (pm active coupleur, networks),

	return (
		<div className='flex justify-between'>
			<div className='flex'>
				{pm && (
					<>
						<div className='mr-10 max-w-40'>
							<img src='/assets/images/fiber/order/pm.png' alt='pm' />
							{/* TODO: mettre la data */}
							{/* <p className='caption mt-2 text-gray-500'> */}
							{/*	Z4008 - PM trencent 21 000 cassette à souder sur voie */}
							{/* </p> */}
						</div>
						<div>
							<div className='mb-7 flex space-x-2'>
								{pm?.type && (
									<Tooltip content='Type de zone de déploiement (Zone Moyennement ou Très Dense)' position='top'>
										<Tag className='badge-gray'>{EPMType[pm?.type]}</Tag>
									</Tooltip>
								)}
								<Tooltip content='ID de PM tel que défini dans le SI flux (IW)' position='top'>
									<a
										href={
											pm?.iwId
												? `https://iw.ftth.iliad.fr/index.php?action=pm.edit&view=form&crypt/primary_key=${pm?.iwId}`
												: '#'
										}
										target='blank'
										className='text-sm font-semibold text-blue-500 underline'>
										{/* {pm?.referencePrestation} */}
										IW : {pm?.iwId || 'N/A'}
									</a>
								</Tooltip>
							</div>
							<div>
								<Tooltip content='Nom du PM Technique tel que défini dans le SI interop' position='top'>
									{pm?.technicalName}
								</Tooltip>
							</div>
							{/* <div>
								<Tooltip content='Nom du coupleur tel que défini dans le SI Free' position='top'>
									{network?.nroActiveCoupler.name}
								</Tooltip>
							</div> */}
							<div>
								<Tooltip content='PM name' position='top'>
									{order.currentOis?.pmName}
								</Tooltip>
							</div>
							<div>
								<Tooltip content='Exploitant' position='top'>
									{order.currentOis?.name}
								</Tooltip>
							</div>
						</div>
					</>
				)}
			</div>
			<div className='flex items-center'>
				<div className='mr-3 flex flex-col items-center justify-center rounded-lg border border-gray-50 bg-white px-8 py-4 text-center dark:border-free-gray-600 dark:bg-free-dark-700'>
					<Typography type='caption' color='gray'>
						Tiroir
					</Typography>
					<Tooltip content='Numéro du tiroir de la position en baie passive' position='top'>
						<Typography className='mt-1 text-xl'>{rop?.nroPassive?.[0]?.drawer || 'N/A'}</Typography>
					</Tooltip>
				</div>
				<div className='mr-3 flex flex-col items-center justify-center rounded-lg border border-gray-50 bg-white px-8 py-4 text-center dark:border-free-gray-600 dark:bg-free-dark-700'>
					<Typography type='caption' color='gray'>
						Colonne
					</Typography>
					<Tooltip content='Lettre de la colonne de la position en baie passive' position='top'>
						<Typography className='mt-1 text-xl'>{rop?.nroPassive?.[0]?.column || 'N/A'}</Typography>
					</Tooltip>
				</div>
				<div className='mr-3 flex flex-col items-center justify-center rounded-lg border border-gray-50 bg-white px-8 py-4 text-center dark:border-free-gray-600 dark:bg-free-dark-700'>
					<Typography type='caption' color='gray'>
						Ligne
					</Typography>
					<Tooltip content='Chiffre de la ligne de la position en baie passive' position='top'>
						<Typography className='mt-1 text-xl'>{rop?.nroPassive?.[0]?.line || 'N/A'}</Typography>
					</Tooltip>
				</div>
			</div>
		</div>
	);
}
export default PmDetails;
