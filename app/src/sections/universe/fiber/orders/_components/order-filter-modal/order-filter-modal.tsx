// utils
import { Input, Modal, ModalBody, ModalHeader } from '@tools/reactore';
import { capitalize } from 'lodash';
// api
import { EFiberOrderState, type TFiberOrderState } from '@/api/interface/fiber';
// sections
import { useSearchOrderFilters } from '../../../../../_components/hooks/use-search-order-filters';
// types
import type { TBaseModalProps } from '@/types/modal.interface';

// ----------------------------------------------------------------------

export default function OrderFilterModal({ isOpen, onClose }: TBaseModalProps) {
	const [ordersFilters, setOrderFilters] = useSearchOrderFilters();

	return (
		<Modal isOpen={isOpen} onClose={onClose} size='md' className='dark:bg-free-dark-slate dark:text-gray-100'>
			<ModalHeader title='Filtres de commandes' hasCloseButton />
			<ModalBody className='h-44'>
				<Input
					name='state'
					type='select'
					placeholder='Statut'
					value={ordersFilters.state}
					selectOptions={{
						items: [
							{
								label: 'Aucun',
								value: 'Aucun'
							},
							...Object.keys(EFiberOrderState).map((key) => ({
								label: capitalize(EFiberOrderState[key]),
								value: key
							}))
						],
						onChange: (e) => {
							const state = e.value as TFiberOrderState & 'Aucun';
							if (state !== 'Aucun') {
								setOrderFilters((prev) => ({ ...prev, state }));
							} else {
								setOrderFilters((prev) => ({ ...prev, state: undefined }));
							}
						}
					}}
				/>
			</ModalBody>
		</Modal>
	);
}
