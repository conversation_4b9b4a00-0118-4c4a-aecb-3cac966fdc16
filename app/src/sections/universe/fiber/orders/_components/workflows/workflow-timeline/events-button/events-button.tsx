import { useEffect, useRef, useState } from 'react';
// utils
import { workflowsStates } from '@/utils/workflows';
import { formatDate, InPortal, Typography, useOnClickOutside } from '@tools/reactore';
import { produce } from 'immer';
import { findKey } from 'lodash';
import { twJoin } from 'tailwind-merge';
// api
import type { IFiberHistory } from '@/api/interface/fiber';
// components
import { CrossIcon } from '@/components-old/ui/icon';
// types
import EventTypeSection from './event-type-section';

// ----------------------------------------------------------------------

type TSortEvents = {
	created: Partial<IFiberHistory>[];
	blocked: Partial<IFiberHistory>[];
	error: Partial<IFiberHistory>[];
	success: Partial<IFiberHistory>[];
};

type Props = {
	event: Partial<IFiberHistory>[];
	workflowName: string;
};

export default function EventsButton({ event, workflowName }: Props) {
	const buttonRef = useRef<HTMLButtonElement>(null);
	const divRef = useRef<HTMLDivElement>(null);

	const [show, setShow] = useState(false);
	const [position, setPosition] = useState({ top: '0px', left: '0px' });

	useEffect(() => {
		if (buttonRef.current) {
			setPosition({
				top: `${buttonRef.current.getBoundingClientRect().top + window.scrollY}px`,
				left: `${buttonRef.current.getBoundingClientRect().left + buttonRef.current.clientWidth + 2}px`
			});
		}
	}, [buttonRef.current?.getBoundingClientRect().left, window.scrollY]);

	useOnClickOutside(divRef, () => setShow(false));

	const sortEvents = event.reduce<TSortEvents>(
		(acc, curr) => {
			const state = findKey(workflowsStates[workflowName], (o) => {
				return o.includes(curr.toState);
			});

			switch (state) {
				case 'created':
					return produce(acc, (draft) => {
						draft.created.push(curr);
					});
				case 'blocked':
					return produce(acc, (draft) => {
						draft.blocked.push(curr);
					});
				case 'success':
					return produce(acc, (draft) => {
						draft.success.push(curr);
					});
				case 'error':
					return produce(acc, (draft) => {
						draft.error.push(curr);
					});
				default:
					return acc;
			}
		},
		{ created: [], blocked: [], error: [], success: [] }
	);

	const firstEvent = event[0];

	return (
		<div className='relative'>
			<button
				ref={buttonRef}
				className={twJoin(
					'flex h-full items-center rounded-full border border-free-gray-300 bg-white px-2 hover:border-free-gray-700',
					show && 'border-free-gray-700'
				)}
				onClick={() => setShow(true)}>
				{event.length}
			</button>
			{show && (
				<InPortal>
					<div
						ref={divRef}
						className={twJoin(
							'card absolute z-20 min-w-105 animate-[email-rocket_300ms_linear_forwards] rounded-lg bg-opacity-100 px-6 py-4 hover:cursor-default'
						)}
						style={position}>
						<div className='flex justify-between'>
							<Typography type='h3' fontFamily='montserrat' className='text-base font-semibold'>
								{event.length} évènements &nbsp; le &nbsp;
								{event.length && firstEvent?.createdAt && `${formatDate(new Date(firstEvent.createdAt), 'dd/MM/yy')}`}
							</Typography>
							<button onClick={() => setShow(false)}>
								<CrossIcon className='w-3' />
							</button>
						</div>
						<div className='mt-4'>
							{Boolean(sortEvents.created.length) && (
								<EventTypeSection events={sortEvents.created} type='created' workflowName={workflowName} />
							)}
							{Boolean(sortEvents.blocked.length) && (
								<EventTypeSection events={sortEvents.blocked} type='blocked' workflowName={workflowName} />
							)}
							{Boolean(sortEvents.error.length) && (
								<EventTypeSection events={sortEvents.error} type='error' workflowName={workflowName} />
							)}
							{Boolean(sortEvents.success.length) && (
								<EventTypeSection events={sortEvents.success} type='success' workflowName={workflowName} />
							)}
						</div>
					</div>
				</InPortal>
			)}
		</div>
	);
}
