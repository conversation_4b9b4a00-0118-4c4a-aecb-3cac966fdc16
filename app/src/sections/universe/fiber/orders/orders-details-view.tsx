import { useQuery } from '@tanstack/react-query';
// utils
import { Typography } from '@tools/reactore';
import { twJoin } from 'tailwind-merge';
// api
import { queries } from '@/api/queries';
// hooks
import { useRouting } from '@/hooks';
import { useOrder } from '@/hooks/queries/use-order';
// sections
import Appointments from './_components/appointments';
import ClientInformations from './_components/client-informations';
import ClientOffer from './_components/client-offer';
import Communications from './_components/communications';
import FtthOrdersHeader from './_components/fiber-ftth-orders-header';
import Fixes from './_components/fixes';
import Invoices from './_components/invoices';
import LineTypology from './_components/line-typology';
import Logistic from './_components/logistic';
import Migrations from './_components/migrations';
import MultisiteDeployment from './_components/multisite-deployment';
import NetworkHistory from './_components/network-history';
import OIHistory from './_components/oihistory';
import OrderEvents from './_components/order-events';
import OrderMap from './_components/order-map';
import OrderTicketsIncidents from './_components/order-tickets-incidents';
import OrderTimeline from './_components/order-timeline';
import Workflows from './_components/workflows';
import { useOrderFilter } from '../../../_components/atom/use-order-filter';
import { CustomPageHeader } from '@/components/custom';
import { useEffect, useRef, useState } from 'react';
import {
	ArrowCircleRight,
	BookOpen01,
	CalendarPlus02,
	Copy01,
	FilePlus02,
	Lightning01,
	PackageSearch,
	Pencil01,
	RefreshCw01,
	RefreshCw05,
	Share04,
	Snowflake01,
	Ticket01
} from '@tools/reactor-icons';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { XCircle } from 'lucide-react';
import { toUpper } from 'lodash';
import dayjs from 'dayjs';
import { toast } from '@/hooks/use-toast.ts';
import { Link } from '@tanstack/react-router';
import VipSwitch from '@/sections/universe/fiber/orders/_components/pro-action-menu/vip-switch.tsx';
import CancelOrderModal from '@/sections/universe/fiber/orders/_components/action-button/action-menu/cancel-order-modal/cancel-order-modal.tsx';
import EditOrderModal from '@/sections/universe/fiber/orders/_components/edit-order-modal/edit-order-modal.tsx';
import { getEraLabel } from '@/components/custom/state-badge';
import type { EraType } from '@/api/interface';

const navItems = [
	{
		id: 'referentiel',
		label: 'Référentiel',
		hasIndicator: false
	},
	{
		id: 'typologie',
		label: 'Typologie',
		hasIndicator: false
	},
	{
		id: 'client',
		label: 'Client',
		hasIndicator: false
	},
	{
		id: 'timeline',
		label: 'Timeline',
		hasIndicator: false
	},
	{
		id: 'rdv',
		label: 'RDV',
		hasIndicator: true
	},
	{
		id: 'tickets',
		label: 'Tickets',
		hasIndicator: true
	},
	{
		id: 'outils',
		label: 'Outils',
		hasIndicator: false
	}
];

export function OrdersDetailsView() {
	const [activeSection, setActiveSection] = useState('referentiel');
	const [indicatorStyle, setIndicatorStyle] = useState({});
	const [isManualScroll, setIsManualScroll] = useState(false);
	const [isCancelOrderModalOpen, setIsCancelOrderModalOpen] = useState(false);
	const [isEditOrderModalOpen, setIsEditOrderModalOpen] = useState(false);

	const navRef = useRef<HTMLDivElement>(null);
	const sectionRefs = useRef<{ [key: string]: HTMLElement | null }>({});
	const navItemRefs = useRef<{ [key: string]: HTMLLIElement | null }>({});

	const clickedSectionRef = useRef<string | null>(null);

	useEffect(() => {
		navItems.forEach((item) => {
			sectionRefs.current[item.id] = document.getElementById(item.id);
		});
	}, []);

	const { push } = useRouting();
	const {
		order,
		oi,
		queryOrder: { isError, error }
	} = useOrder();
	const [, _setOrderFilter] = useOrderFilter();

	const { data: businessProjects } = useQuery({
		...queries.fiber_company.byId(order?.company?.['@id'])._ctx.businessProjects(),
		enabled: Boolean(order?.company)
	});

	const migrationsIn = oi?.migrations?.filter((migrationItem) => migrationItem.direction === 'inbound');
	const migrationsOut = oi?.migrations?.filter((migrationItem) => migrationItem.direction === 'outbound');

	const hasMigration = Boolean(migrationsIn?.length || migrationsOut?.length);

	useEffect(() => {
		const handleScroll = () => {
			if (isManualScroll) return;

			const scrollTop = window.scrollY;
			const scrollHeight = document.documentElement.scrollHeight;
			const clientHeight = document.documentElement.clientHeight;

			const scrollbarThumbPosition = scrollTop / (scrollHeight - clientHeight);

			let activeSectionId = navItems[0].id;

			const sectionPositions = navItems.map((item) => {
				const section = sectionRefs.current[item.id];
				if (!section) return { id: item.id, start: 0, end: 0 };

				const sectionTop = section.offsetTop;
				const sectionHeight = section.offsetHeight;

				const sectionStart = sectionTop / scrollHeight;
				const sectionEnd = (sectionTop + sectionHeight) / scrollHeight;

				return {
					id: item.id,
					start: sectionStart,
					end: sectionEnd
				};
			});

			for (const section of sectionPositions) {
				if (scrollbarThumbPosition >= section.start && scrollbarThumbPosition <= section.end) {
					activeSectionId = section.id;
					break;
				}
			}

			if (scrollbarThumbPosition > 0.95) {
				activeSectionId = navItems[navItems.length - 1].id;
			}

			setActiveSection(activeSectionId);
		};

		window.addEventListener('scroll', handleScroll);
		handleScroll();

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	}, [isManualScroll]);

	const handleNavClick = (sectionId: string) => {
		setActiveSection(sectionId);

		setIsManualScroll(true);
		clickedSectionRef.current = sectionId;

		const section = document.getElementById(sectionId);
		if (section) {
			const navHeight = navRef.current?.offsetHeight || 0;
			const sectionTop = section.offsetTop - navHeight - 20; // Add some padding

			window.scrollTo({
				top: sectionTop,
				behavior: 'smooth'
			});

			setTimeout(() => {
				setIsManualScroll(false);
			}, 1000);
		}
	};

	useEffect(() => {
		const activeItem = navItemRefs.current[activeSection];
		if (activeItem && navRef.current) {
			const navRect = navRef.current.getBoundingClientRect();
			const itemRect = activeItem.getBoundingClientRect();

			setIndicatorStyle({
				left: `${itemRect.left - navRect.left}px`,
				width: `${itemRect.width}px`
			});
		}
	}, [activeSection]);

	if (isError) {
		const { status } = error as any;
		const isErrorOther = (error as any).status > 404 || (error as any).status > 401 || (error as any).status === 400;

		return (
			<div className='relative z-10 flex w-full h-full mx-auto flex-center'>
				<Typography type='h4' className='text-center text-free-state-error'>
					{status === 404 && "Cette commande n'existe pas"}
					{status === 403 &&
						"Droits d'accès insuffisant. Essayez de vous déconnecter puis de vous reconnecter si le problème persiste. "}
					{status !== 404 && status !== 403 && 'Une erreur est survenue'}
					{isErrorOther && (
						<>
							<br />
							<Typography type='caption' className='text-center'>
								Erreur {status}
							</Typography>
						</>
					)}
				</Typography>
			</div>
		);
	}

	const getOfferLabel = (offerName?: string, wholesaleReference?: string): string => {
		if (!offerName) return '';

		switch (offerName) {
			case 'Offre fibre avec Freebox Pro':
				return 'FREE Pro';
			case 'Offre FTTH activée lien nu':
				return wholesaleReference ? 'Wholesale' : 'XPR';
			case 'Offre FTTO':
				return 'FTTO';
			default:
				return offerName;
		}
	};

	const getDataToCopy = () => {
		const getRdv = () => {
			if (order?.appointments?.[0]?.appointmentSchedules) {
				if (dayjs(order?.appointments?.[0]?.appointmentSchedules?.[0]?.scheduledAt).isBefore(dayjs())) {
					return `Dernier RDV le ${order?.appointments?.[0]?.appointmentSchedules?.[0]?.scheduledAt}`;
				}
				return `Prochain RDV le ${order?.appointments?.[0]?.appointmentSchedules?.[0]?.scheduledAt}`;
			}
			return 'Pas de RDV prévu';
		};
		return `[${toUpper(order?.type)} ${getOfferLabel(order?.offerOrders?.[0]?.offer?.name, order?.wholesaleReference)}] Commande ${order?.reference} du ${dayjs(order?.createdAt).format('DD/MM/YYYY')} en ${getEraLabel(order?.era as EraType)}
${order?.endpointAddress?.street} ${order?.endpointAddress?.postalCode} ${order?.endpointAddress?.city} ${order?.currentOis?.areaType ? `[${order?.currentOis?.areaType}]` : ''}
- Client : ${order?.company?.name} [${order?.retailerReference}]
- NRO : ${order?.currentRops?.nro?.name}
- PM : ${order?.currentRops?.pm?.name} [${order?.currentOis?.technology}]
- PTO : ${order?.currentRops?.pto?.name} [${order?.reference}]
${order?.type === 'ftto' ? `- OI : ${oi?.operatorName} @ ${oi?.reference}` : ''}
${order?.currentOis?.isMigration ? `Migration depuis B2C ${oi?.reference}` : ''}
${getRdv()}
Voir sur ProUI : https://proui.services.b2b.iliad.fr/fiber/orders/${order?.reference}`;
	};

	return (
		<>
			<CustomPageHeader
				title='Commande FTTH'
				canGoBack
				onBack={() => push('/fiber/orders')}
				fullContent={
					<div>
						<nav
							ref={navRef}
							className='top-0 relative z-50 flex items-center justify-center p-1.5 bg-card max-w-2xl mx-auto rounded-full'>
							<div
								className='absolute h-8 px-4 bg-[#e0e0e0] rounded-full transition-all duration-300 ease-in-out'
								style={indicatorStyle}
							/>
							<ul className='flex items-center justify-between w-full relative z-10'>
								{navItems.map((item) => (
									<li key={item.id} ref={(el) => (navItemRefs.current[item.id] = el) as any} className='relative group'>
										{item.hasIndicator && (
											<div className='absolute -right-1 top-1 transform -translate-x-1/2 w-2 h-2 bg-[#9333ea] rounded-full' />
										)}
										<a
											href={`#${item.id}`}
											className={`flex items-center justify-center px-4 py-1 text-sm font-medium transition-colors ${
												activeSection === item.id ? 'text-[#161616]' : 'text-foreground'
											}`}
											onClick={(e) => {
												e.preventDefault();
												handleNavClick(item.id);
											}}>
											{item.label}
										</a>
									</li>
								))}
							</ul>
						</nav>
					</div>
				}
			/>

			<div className='flex-1 px-4 py-1 overflow-auto lg:flex-row lg:space-x-12 lg:space-y-0 mt-2'>
				<div className='flex'>
					<div className='flex-1 w-full min-w-0 dark:text-white'>
						<div key='referentiel' id={'referentiel'}>
							<FtthOrdersHeader />
							<Fixes />
							<OrderMap />
						</div>
						<div key='typologie' id={'typologie'}>
							<LineTypology />
						</div>
						<div key='client' id={'client'}>
							<ClientInformations />
						</div>
						<div key='timeline' id={'timeline'}>
							<OrderTimeline />

							{businessProjects?.['hydra:member']?.length ? <MultisiteDeployment /> : null}
							<Communications />
						</div>
						<div className='desktop:grid desktop:grid-cols-2 desktop:gap-x-4' id='rdv' key='rdv'>
							<Appointments />
							<ClientOffer />
						</div>
						<div id='tickets' key='tickets'>
							<div className={twJoin('flex w-full gap-4', !hasMigration && 'flex-col')}>
								{hasMigration && (
									<div className='basis-1/2'>
										<Migrations />
									</div>
								)}
								<div
									className={twJoin(
										hasMigration
											? 'flex basis-1/2 flex-col desktop:gap-y-4'
											: 'desktop:grid desktop:grid-cols-2 desktop:gap-x-4'
									)}>
									<Invoices />
									<OrderEvents />
								</div>
							</div>
							<OrderTicketsIncidents />
						</div>
						<div id='outils' key='outils'>
							<Logistic />
							<Workflows />
							<div className='desktop:grid desktop:grid-cols-2 desktop:gap-x-4'>
								<OIHistory />
								<NetworkHistory />
							</div>
						</div>
					</div>
				</div>
			</div>

			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<div
						className={
							'fixed z-50 bottom-5 right-5 h-12 w-12 flex items-center justify-center p-1.5 bg-card max-w-2xl mx-auto rounded-xl shadow-lg cursor-pointer'
						}>
						<Lightning01 variant={'solid'} className='h-6 w-6 text-blue-500' />
					</div>
				</DropdownMenuTrigger>
				<DropdownMenuContent side={'top'} align={'end'} className='w-96 border-none rounded-xl p-2'>
					<DropdownMenuLabel className='text-md flex justify-between'>
						Menu ProAction
						<VipSwitch />
					</DropdownMenuLabel>
					<DropdownMenuSeparator />
					<DropdownMenuLabel>Partage rapide</DropdownMenuLabel>
					<DropdownMenuItem
						className={'text-primary'}
						onClick={() => {
							navigator.clipboard.writeText(getDataToCopy());
							toast({
								title: 'Informations copiées',
								description: 'Les informations ont été copiées dans le presse-papiers.',
								duration: 2000
							});
						}}>
						<Copy01 size={16} />
						Copier les informations
					</DropdownMenuItem>
					<DropdownMenuItem
						className={'text-primary'}
						onClick={() => {
							navigator.clipboard.writeText(window.location.href);
							toast({
								title: 'Lien copié',
								description: 'Le lien a été copié dans le presse-papiers.',
								duration: 2000
							});
						}}>
						<Share04 size={16} />
						Envoyer le lien
					</DropdownMenuItem>
					<DropdownMenuLabel className='mt-2'>Gestion rapide</DropdownMenuLabel>
					<DropdownMenuItem>
						<RefreshCw05 /> Synchroniser les infos
					</DropdownMenuItem>
					<Link
						to={'/tickets/fiber'}
						search={{
							type: 'ftth',
							order: order?.reference
						}}>
						<DropdownMenuItem>
							<Ticket01 />
							Créer un ticket
						</DropdownMenuItem>
					</Link>
					<DropdownMenuItem>
						<CalendarPlus02 />
						Prendre un RDV
					</DropdownMenuItem>
					<DropdownMenuItem>
						<FilePlus02 />
						Créer un devis
					</DropdownMenuItem>
					<DropdownMenuItem>
						<PackageSearch />
						Vérifier l’éligibilité
					</DropdownMenuItem>
					<DropdownMenuItem>
						<BookOpen01 />
						Historique d’actions
					</DropdownMenuItem>
					<DropdownMenuLabel className='mt-2'>Zone de danger</DropdownMenuLabel>
					<DropdownMenuItem>
						<RefreshCw01 />
						Relancer la commande
					</DropdownMenuItem>
					<DropdownMenuItem onClick={() => setIsEditOrderModalOpen(true)}>
						<Pencil01 />
						Modifier les paramètres de commande
					</DropdownMenuItem>
					<DropdownMenuItem>
						<ArrowCircleRight />
						Changer le mode de provisionnement
					</DropdownMenuItem>
					<DropdownMenuItem onClick={() => setIsCancelOrderModalOpen(true)}>
						<XCircle />
						Résilier la commande
					</DropdownMenuItem>
					<DropdownMenuItem>
						<Snowflake01 />
						Geler la commande
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Modals */}
			<CancelOrderModal isOpen={isCancelOrderModalOpen} onClose={() => setIsCancelOrderModalOpen(false)} />
			<EditOrderModal isOpen={isEditOrderModalOpen} onClose={() => setIsEditOrderModalOpen(false)} />
		</>
	);
}
