import { useState, useEffect } from 'react';
// tanstack
import { useQueryClient } from '@tanstack/react-query';
import { useParams } from '@tanstack/react-router';
// utils
import { cn } from '@/lib/utils';
import {
	Attachment01,
	Bold01,
	Brush01,
	ChevronUp,
	Code01,
	Italic01,
	Send03,
	Strikethrough01,
	Underline01,
	XClose
} from '@tools/reactor-icons';
import { Typography, formatOctetTo } from '@tools/reactore';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { v4 as uuidv4 } from 'uuid';
import { Editor, Transforms, Range as SlateRange } from 'slate';
import { ReactEditor } from 'slate-react';
// api
import type { IMagnetMessage, TMagnetMessagesData } from '@/api/schemas';
import { getTicketStateValue } from '@/api/enums';
import { magnetFetcher } from '@/api/fetcher';
import { useMutation } from '@/api/fetcher/mutation-service.ts';
import { useMagnetDocumentMutation } from '@/api/mutations/magnet/useMagnetDocument.mutation.ts';
import { magnetQueryKeys } from '@/api/queries';
import { useMagnetTicketQuery } from '@/api/queries/magnet/useMagnetTicket.query.ts';
// contexts
import { useAuthContext } from '@/auth/auth-context.tsx';
// sections
import DropFile from './drop-file';
import { ActionStatusDropdown } from './dropdown';
import { CloseTicketModal, FeedBackTicketModal, type TFeedBackType } from './modals';
// components
import CustomEditor, { type TDataEditor } from '@/components/custom/custom-editor';
import { Button } from '@/components/ui/button.tsx';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip.tsx';
// others
import useTicketAtom from '../../ticket.atom';
import ActionTicketModal from '@/sections/universe/tickets/fiber/_components/tickets-details/messenger/messenger-bar/modals/action-ticket-modal.tsx';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Quote } from 'lucide-react';

// ----------------------------------------------------------------------

type TUploadFilesData = Record<
	string,
	{
		file: File;
		id?: string;
		error?: string;
	}
>;

export default function MessengerBar() {
	const queryClient = useQueryClient();
	const { id } = useParams({
		strict: false
	});

	const { hasAccess } = useAuthContext();

	//  QUERY ---------------------------------------------------------------------------------------------------------
	const { ticket } = useMagnetTicketQuery();

	/*const activeOutsources = useQueries({
		queries: ticket?.activeOutsources?.length
			? ticket?.activeOutsources?.map((activeOutsourceItem: any) => {
					return {
						...magnetQueryKeys.magnet_outsource.byId(activeOutsourceItem['@id']),
						enabled: Boolean(ticket && activeOutsourceItem['@id'])
					};
				})
			: []
	});*/

	//  STATE ---------------------------------------------------------------------------------------------------------
	const [ticketParams] = useTicketAtom();
	const [messageDataEditor, setMessageDataEditor] = useState<TDataEditor>({
		isReset: false,
		value: [],
		valueText: ''
	});
	const [typeFeedBack, _setTypeFeedBack] = useState<TFeedBackType | undefined>(undefined);
	const [filesUpload, setFilesUpload] = useState<TUploadFilesData>({});
	const [isFeedBackModalOpen, setIsFeedBackModalOpen] = useState(false);
	const [isAwaitingClosureTicketModalOpen, setIsAwaitingClosureTicketModalOpen] = useState(false);
	const [actionModalType, setActionModalType] = useState('');
	const [isActionTicketModalOpen, setIsActionTicketModalOpen] = useState(false);
	const [isShowUploadFile, setIsShowUploadFile] = useState(false);

	//  MUTATION ---------------------------------------------------------------------------------------------------------
	const postMessageMutation = useMutation<IMagnetMessage, TMagnetMessagesData>({
		mutationFn: (data) =>
			magnetFetcher({
				url: `/messages`,
				data,
				method: 'post'
			}),
		onSuccess: () => {
			queryClient
				.invalidateQueries({
					queryKey: magnetQueryKeys.magnet_ticket.byId(id)._ctx.messages._def
				})
				.then(() => {
					setMessageDataEditor({
						value: [],
						valueText: '',
						isReset: true
					});
					setFilesUpload({});
				});
		}
	});
	const mutationDocumentPost = useMagnetDocumentMutation();

	//  FUNCTION ---------------------------------------------------------------------------------------------------------
	const fetcherMessagePost = async () => {
		const outsourcesArray: string[] = [];
		const filesArray: string[] = [];

		if (ticketParams?.outsources) {
			Object.entries(ticketParams?.outsources).forEach(([key, value]) => {
				if (value) {
					outsourcesArray.push(key);
				}
			});
		}

		if (valueText && valueText.trim().length) {
			for (let i = 0; i < filesUploadEntries.length; i++) {
				const fileData = filesUploadEntries[i];

				if (fileData) {
					const formDataDocument = new FormData();
					const [, { file }] = fileData;

					formDataDocument.append('file', file);

					const data = await mutationDocumentPost.mutateAsync(formDataDocument);

					filesArray.push(data['@id']);
				}
			}
			console.log('ticketParams', ticketParams);

			postMessageMutation.mutate({
				documents: filesArray,
				outsources: outsourcesArray,
				content: valueText as string,
				ticket: ticket?.['@id'] as string,
				team: ticketParams?.team as string
			});
		}
	};
	const handleFeedBack = (type: TFeedBackType) => {
		setActionModalType(type);
		setIsActionTicketModalOpen(true);
	};

	// Function to apply markdown formatting to selected text
	const applyMarkdownSyntax = (syntax: string) => {
		const slateEditor = messageDataEditor.editor;
		if (!slateEditor) return;

		const { selection } = slateEditor;
		if (!selection) return;

		const isCollapsed = SlateRange.isCollapsed(selection);
		const _selectedText = isCollapsed ? '' : Editor.string(slateEditor, selection);

		let startSyntax = '';
		let endSyntax = '';

		switch (syntax) {
			case 'bold':
				startSyntax = '**';
				endSyntax = '**';
				break;
			case 'italic':
				startSyntax = '*';
				endSyntax = '*';
				break;
			case 'strikethrough':
				startSyntax = '~~';
				endSyntax = '~~';
				break;
			case 'underline':
				startSyntax = '__';
				endSyntax = '__';
				break;
			case 'code':
				startSyntax = '`';
				endSyntax = '`';
				break;
			case 'quote':
				startSyntax = '> ';
				endSyntax = '';
				break;
			default:
				break;
		}

		ReactEditor.focus(slateEditor);

		if (isCollapsed) {
			Transforms.insertText(slateEditor, startSyntax + endSyntax);
			Transforms.move(slateEditor, { distance: endSyntax.length, reverse: true });
		} else {
			Transforms.insertText(slateEditor, endSyntax, { at: SlateRange.end(selection) });
			Transforms.insertText(slateEditor, startSyntax, { at: SlateRange.start(selection) });
		}
	};

	const handleKeyDown = (event: KeyboardEvent) => {
		const slateEditor = messageDataEditor.editor;
		if (!slateEditor) return;

		const { selection } = slateEditor;
		if (!selection || SlateRange.isCollapsed(selection)) return; // Only apply when text is selected

		const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
		const modKey = isMac ? event.metaKey : event.ctrlKey;

		if (!modKey) return;

		switch (event.key.toLowerCase()) {
			case 'b':
				event.preventDefault();
				applyMarkdownSyntax('bold');
				break;
			case 'i':
				event.preventDefault();
				applyMarkdownSyntax('italic');
				break;
			case 'u':
				event.preventDefault();
				applyMarkdownSyntax('underline');
				break;
			default:
				break;
		}
	};

	useEffect(() => {
		document.addEventListener('keydown', handleKeyDown);
		return () => {
			document.removeEventListener('keydown', handleKeyDown);
		};
	}, [messageDataEditor.editor]);

	//  VARIABLE ---------------------------------------------------------------------------------------------------------
	const { valueText } = messageDataEditor;
	const { state } = ticket || {};
	const filesUploadEntries = Object.entries(filesUpload);
	//const outsources = activeOutsources.filter(({ data }) => data).map(({ data }) => data);
	const stateValueById = getTicketStateValue(state);
	const isStateOpened = stateValueById === '/ticket_state_enums/OPENED';
	const isStateAwaitingClosure = stateValueById === '/ticket_state_enums/AWAITING_CLOSURE';
	const isSendMessageDiabled = !valueText?.trim()?.length && (isStateOpened || isStateAwaitingClosure);
	const isShowBar = (isStateOpened || isStateAwaitingClosure) && hasAccess('ROLE_REPLY_TICKET');

	return (
		<>
			{isShowBar && (
				<section className='bottom-0 hidden w-full flex-none flex-col gap-y-2 rounded-2xl  bg-card p-2 has-[section[data-show="true"]]:flex dark:bg-dark-smoke'>
					<section data-show={isShowBar} className='space-y-2'>
						{/* ======== ACTION BAR ======== */}
						<section className='flex'>
							<section className='flex w-1/2 justify-start'>
								<ActionStatusDropdown state={stateValueById} handleFeedBack={handleFeedBack} />
							</section>
							<section className='flex w-1/2 justify-end gap-x-2'>
								<Button
									variant='ghost'
									className={cn(
										'h-7.5 w-15.5 rounded-4xl border-solid bg-accent/80 px-2.5 py-1.5',
										isShowUploadFile && 'bg-accent'
									)}
									onClick={() => {
										setIsShowUploadFile((prev) => !prev);
									}}>
									<Attachment01 className='h-4.5 ' />
								</Button>
								<DropdownMenu>
									<DropdownMenuTrigger asChild>
										<Button
											variant='ghost'
											className={cn(
												'h-7.5 w-15.5 rounded-4xl border-solid bg-accent/80 px-2.5 py-1.5  [&_svg]:data-[state=open]:rotate-180'
											)}>
											<Brush01 className='h-4.5 !rotate-0 ' />
											<ChevronUp className='h-4.5 ml-1' />
										</Button>
									</DropdownMenuTrigger>
									<DropdownMenuContent className={'!min-w-0'}>
										<DropdownMenuItem onClick={() => applyMarkdownSyntax('bold')}>
											<Bold01 />
										</DropdownMenuItem>
										<DropdownMenuItem onClick={() => applyMarkdownSyntax('italic')}>
											<Italic01 />
										</DropdownMenuItem>
										<DropdownMenuItem onClick={() => applyMarkdownSyntax('underline')}>
											<Underline01 />
										</DropdownMenuItem>
										<DropdownMenuItem onClick={() => applyMarkdownSyntax('strikethrough')}>
											<Strikethrough01 />
										</DropdownMenuItem>
										<DropdownMenuItem onClick={() => applyMarkdownSyntax('quote')}>
											<Quote />
										</DropdownMenuItem>
										<DropdownMenuItem onClick={() => applyMarkdownSyntax('code')}>
											<Code01 />
										</DropdownMenuItem>
									</DropdownMenuContent>
								</DropdownMenu>

								{/*Boolean(outsources.length) && <OutsourceSelectDropdown outsources={outsources || []} />*/}
							</section>
						</section>

						{/* ======== PREVIEW FILE UPLOAD BAR ======== */}
						{Boolean(filesUploadEntries.length) && (
							<section className='flex gap-x-2 overflow-x-auto'>
								{filesUploadEntries?.map(([uuid, { file }]) => {
									return (
										<div
											key={`${file.name}-${uuid}`}
											className=' flex h-6 flex-none items-center rounded-full bg-[#F3F4FC] px-2 py-0.5'>
											<Typography className='mr-2 flex h-full items-center text-3'>
												<TooltipProvider>
													<Tooltip>
														<TooltipTrigger className='max-w-28.25 cursor-default truncate text-gray-900/50'>
															{file.name}
														</TooltipTrigger>
														<TooltipContent className='bg-[#F3F4FC] text-gray-900'>
															<Typography>{file.name}</Typography>
														</TooltipContent>
													</Tooltip>
												</TooltipProvider>
												<div className='mx-2 h-4 w-0 border-r border-r-free-gray-400' />
												<span>{formatOctetTo(file.size)}</span>
											</Typography>
											<Button
												variant='ghost'
												className='h-4.5'
												onClick={() => {
													delete filesUpload[uuid];

													setFilesUpload({
														...filesUpload
													});
												}}>
												<XClose className='h-4.5' />
											</Button>
										</div>
									);
								})}
							</section>
						)}

						{/* ======== INPUT BAR ======== */}
						{!isShowUploadFile && (
							<section className='flex w-full items-end gap-x-2 rounded-xl'>
								<CustomEditor
									className='max-h-50 min-h-8 flex-1 overflow-auto rounded-xl'
									data={messageDataEditor}
									onChange={(data) => {
										setMessageDataEditor(data);
									}}
								/>
								<Button
									className='h-12 w-13'
									isLoading={postMessageMutation.isPending as boolean}
									disabled={isSendMessageDiabled || postMessageMutation.isPending}
									onClick={() => {
										if (valueText && valueText.trim().length) {
											fetcherMessagePost();
										}
									}}>
									<Send03 className='h-5 text-white' />
								</Button>
							</section>
						)}
						{isShowUploadFile && (
							<DndProvider backend={HTML5Backend}>
								<DropFile
									files={filesUploadEntries}
									className='h-20 w-full'
									onRemoveFile={(uuid) => {
										delete filesUpload[uuid];

										setFilesUpload({
											...filesUpload
										});
									}}
									onDrop={(data) => {
										const files = data?.files;
										const newFile: TUploadFilesData = {};

										files.forEach((file) => {
											const uuid = uuidv4();

											newFile[uuid] = {
												file
											};
										});

										if (files?.length) {
											setFilesUpload({
												...filesUpload,
												...newFile
											});
										}
									}}
								/>
							</DndProvider>
						)}
					</section>
				</section>
			)}
			<FeedBackTicketModal isOpen={isFeedBackModalOpen} onClose={() => setIsFeedBackModalOpen(false)} type={typeFeedBack} />
			<CloseTicketModal isOpen={isAwaitingClosureTicketModalOpen} onClose={() => setIsAwaitingClosureTicketModalOpen(false)} />
			<ActionTicketModal
				isOpen={isActionTicketModalOpen}
				onClose={() => setIsActionTicketModalOpen(false)}
				action={actionModalType}
				ticketId={ticket?.['@id'] as string}
			/>
		</>
	);
}
