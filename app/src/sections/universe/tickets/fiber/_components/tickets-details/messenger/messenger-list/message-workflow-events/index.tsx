import React from 'react';
import { Announcement01, ChevronDown } from '@tools/reactor-icons';
import { format, parseISO } from 'date-fns';
import { compareAsc } from 'date-fns';
// tanstack
import { useQueries } from '@tanstack/react-query';
// api
import type { IMagnetTicketHistory } from '@/api/schemas/entities';
import type { IMagnetEvent } from '@/api/schemas/entities';
import { pawnQueryKeys } from '@/api/queries';
// sections
import MessageWorkflowEventItem from './message-workflow-event-item';
// components
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible.tsx';
import { getUserField } from '@/components/fetchers/users.ts';

// ----------------------------------------------------------------------

type Props = {
	histories?: IMagnetTicketHistory[];
	events?: IMagnetEvent[];
};

export default function MessageWorkflowEvents({ histories = [], events = [] }: Props) {
	const ticketEvents = React.useMemo(() => [...histories, ...events], [histories, events]);
	const totalEvents = ticketEvents.length;
	const hasEvents = totalEvents > 0;

	const userIris = React.useMemo(() => {
		if (!hasEvents) return [];

		const irisSet = new Set<string>();
		ticketEvents.forEach((item) => {
			if ('user' in item && item.user?.iri && typeof item.user.iri === 'string') {
				irisSet.add(item.user.iri);
			}
		});
		return Array.from(irisSet);
	}, [ticketEvents, hasEvents]);

	const userQueries = useQueries({
		queries: userIris.map((iri) => ({
			queryKey: pawnQueryKeys.pawn_staff.byId(iri).queryKey,
			queryFn: pawnQueryKeys.pawn_staff.byId(iri).queryFn,
			staleTime: 5 * 60 * 1000 // 5 minutes
		}))
	});

	const userDataMap = React.useMemo(() => {
		const map = new Map<string, any>();
		userIris.forEach((iri, index) => {
			if (userQueries[index]?.data) {
				map.set(iri, userQueries[index].data);
			}
		});
		return map;
	}, [userIris, userQueries]);

	const getUserName = React.useCallback(
		(iri: string | undefined): string => {
			if (!iri) return 'Système';
			const userData = userDataMap.get(iri);
			return userData ? getUserField(userData, 'fullname') : 'Système';
		},
		[userDataMap]
	);

	const sortedTicketEvents = React.useMemo(() => {
		if (!hasEvents) return [];

		return [...ticketEvents].sort((a, b) => {
			try {
				const dateA = typeof a.createdAt === 'string' ? parseISO(a.createdAt) : a.createdAt;
				const dateB = typeof b.createdAt === 'string' ? parseISO(b.createdAt) : b.createdAt;
				const comparison = compareAsc(dateA as unknown as string, dateB as unknown as string);
				return comparison;
			} catch (e) {
				console.error('[MessageWorkflowEvents] Error during date sorting:', a, b, e);
				return 0;
			}
		});
	}, [ticketEvents, hasEvents]);

	const getEventsText = React.useCallback((eventType: string) => {
		switch (eventType) {
			case 'start':
				return 'Inquiry créé';
			case 'appointment_creation':
				return 'Création RDV';
			case 'appointment_start':
				return 'RDV commencé';
			case 'request_closure':
				return 'Cloture demandée';
			case 'refuse_closure':
				return 'Refus de la cloture';
			case 'accept_closure':
				return 'Acceptation de la cloture';
			case 'reminder':
				return 'Relance envoyée';
			default:
				return '';
		}
	}, []);

	const content = React.useMemo(() => {
		if (!hasEvents) {
			return null;
		}

		return (
			<Collapsible className='w-full space-y-2 flex flex-col justify-center items-center mb-4'>
				<CollapsibleTrigger className='flex justify-between items-center text-primary dark:text-white/80 *:data-[state=open]:font-semibold [&>svg[data-svg="chevron"]]:data-[state=open]:rotate-180 bg-primary/5 dark:bg-primary/35 hover:bg-primary/10 rounded-lg px-3 py-1 cursor-pointer data-[state=open]:w-full data-[state=open]:max-w-4xl'>
					<div className='mr-2 flex items-center gap-x-2 text-sm font-semibold'>
						<Announcement01 className='h-4 w-4' />
						{totalEvents} évènement{totalEvents > 1 ? 's' : ''}
					</div>
					<ChevronDown data-svg='chevron' className='h-4 w-4' />
				</CollapsibleTrigger>
				<CollapsibleContent className='border-grey-200 border rounded-lg data-[state=open]:w-full data-[state=open]:max-w-4xl p-3 space-y-2'>
					{sortedTicketEvents.map((item: any, index) => {
						if ('type' in item && !('fromState' in item)) {
							return (
								<div key={item['@id'] || `event-${index}`} className='text-sm'>
									<div className='flex items-center gap-x-2 text-3 text-free-gray-500'>
										{item.createdAt && format(new Date(item.createdAt), 'dd/MM/yyyy HH:mm')}
										<div className={'size-1 bg-free-gray-500 rounded-full'} />
										<span className='text-3 text-free-gray-500'>
											{getUserName(item.user?.iri)} - {getEventsText(item.type)}
										</span>
									</div>
								</div>
							);
						}
						if ('fromState' in item) {
							return <MessageWorkflowEventItem key={item['@id'] || `history-${index}`} history={item} />;
						}
						return null;
					})}
				</CollapsibleContent>
			</Collapsible>
		);
	}, [hasEvents, totalEvents, sortedTicketEvents, getUserName, getEventsText]);

	return content;
}
