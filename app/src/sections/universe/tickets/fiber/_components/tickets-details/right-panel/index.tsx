// utils
import { getColorStateOrder } from '@/utils';
import { ChevronRight } from '@tools/reactor-icons';
import { formatDate, Tag } from '@tools/reactore';
import { AlertCircle } from 'lucide-react';
import { twJoin } from 'tailwind-merge';
// api
import { EFiberOrderState } from '@/api/interface';
// hooks
import { useTicketRightPanel } from '@/hooks/use-ticket-right-panel.tsx';
// components
import { Button } from '@/components/ui/button.tsx';
import { Separator } from '@/components/ui/separator';

// ----------------------------------------------------------------------

export default function RightPanelContent() {
	const { activeTabComponent, orderData } = useTicketRightPanel();

	const renderSiret = () => {
		const firstPartSiret = orderData?.company?.registrationNumber?.slice(0, 8);
		const lastPartSiret = orderData?.company?.registrationNumber?.slice(-5);

		return (
			<>
				{firstPartSiret}
				<span className='opacity-50'>{lastPartSiret}</span>
			</>
		);
	};
	return (
		<div className='bg-card w-[350px] h-full rounded-xl flex flex-col justify-between relative'>
			{/* Status Section */}

			<div className={'h-full flex flex-col'}>
				<section className='flex items-center justify-between px-3 py-2 border-b'>
					{orderData?.state && (
						<Tag className={twJoin(getColorStateOrder(orderData.state), 'capitalize')}>{EFiberOrderState[orderData.state]}</Tag>
					)}
					{orderData?.createdAt && (
						<span className='text-xs text-free-gray-300'>
							le {formatDate(new Date(orderData.createdAt), 'dd/MM/yyyy - HH:mm')}
						</span>
					)}
				</section>
				<div className='flex-1 relative'>
					<div className='absolute top-0 bottom-0 right-0 left-0'>{activeTabComponent}</div>
				</div>
			</div>

			<div className={'absolute w-full bottom-0 bg-card rounded-lg '}>
				<Separator />
				<div className='flex items-center justify-between p-4'>
					<div className='flex items-center gap-2'>
						<div className='h-8 w-8 bg-muted rounded flex items-center justify-center'>
							<AlertCircle className='h-4 w-4' />
						</div>
						<div>
							<p className='font-medium'>{orderData?.company?.name}</p>
							<p className='text-xs text-muted-foreground'>Siret: {renderSiret()}</p>
						</div>
					</div>
					<Button type='button' variant='ghost' size='sm'>
						Voir
						<ChevronRight size={10} />
					</Button>
				</div>
			</div>
		</div>
	);
}
