// tanstack
import { <PERSON> } from '@tanstack/react-router';
// utils
import { Flag01 } from '@tools/reactor-icons';
// api
import { getTicketPriorityValue, getTicketStateLabel, TicketPriorityEnum, TicketStateEnum } from '@/api/enums';
// components
import { Badge } from '@/components/ui/badge.tsx';
import { Card, CardContent } from '@/components/ui/card.tsx';

// ----------------------------------------------------------------------

interface OtherTicketCardProps {
	number?: string;
	title?: string;
	state?: string;
	resolvedDate?: string;
	priority?: string;
}

export function OtherTicketCard({ number, title, state, priority }: OtherTicketCardProps) {
	const priorityValue = getTicketPriorityValue(priority);

	const colorClass =
		{
			[TicketPriorityEnum.LOW]: 'text-blue-500',
			[TicketPriorityEnum.NORMAL]: 'text-yellow-500',
			[TicketPriorityEnum.HIGH]: 'text-orange-500',
			[TicketPriorityEnum.URGENT]: 'text-red-500'
		}[priorityValue as TicketPriorityEnum] || '';

	const stateValue = getTicketPriorityValue(state);

	const variant = stateValue === TicketStateEnum.OPENED ? 'blue' : stateValue === TicketStateEnum.SOLVED ? 'green' : 'destructive';

	return (
		<Link to={`/tickets/${number}` as string}>
			<Card className='mb-3 shadow-none border-none bg-background/30'>
				<CardContent className='p-4'>
					<div className='flex items-start justify-between'>
						<div className='flex items-center gap-2'>
							<span className='text-sm font-medium text-muted-foreground'>#{number}</span>
							{priorityValue && <Flag01 size={18} className={colorClass} />}
						</div>
						<Badge className='capitalize' variant={variant}>
							{getTicketStateLabel(state as string)}
						</Badge>
					</div>
					<p className='mt-2 text-base'>{title}</p>
				</CardContent>
			</Card>
		</Link>
	);
}
