import { useState } from 'react';
// tanstack
import { useParams } from '@tanstack/react-router';
// sections
import CloseTicketModal from './modals/close-ticket-modal';
import FeedBackTicketModal from './modals/feed-back-ticket-modal';
import { ExchangeActionsBarSection } from './section';
// others
import type { TFeedBackType } from './modals/feeb-back-ticket-modal/feed-back-ticket-modal';

// ----------------------------------------------------------------------

type Props = {
	type: 'guichets' | 'exchanges';
};
export default function ActionsBarSection() {
	const { id } = useParams({ strict: false });
	const type = 'exchanges';

	const [typeFeedBack, setTypeFeedBack] = useState<TFeedBackType | undefined>(undefined);
	const [isFeedBackModalOpen, setIsFeedBackModalOpen] = useState(false);
	const [isAwaitingClosureTicketModalOpen, setIsAwaitingClosureTicketModalOpen] = useState(false);

	const handleFeedBack = (type: TFeedBackType) => {
		if (type !== 'answer') {
			setTypeFeedBack(type);
			setIsFeedBackModalOpen(true);
		} else {
			setIsAwaitingClosureTicketModalOpen(true);
		}
	};

	return (
		<>
			<div className='bottom-0 flex flex-col flex-none w-full px-8 py-4 space-x-4 bg-white border-2 border-white dark:bg-dark-smoke rounded-2xl'>
				{type === 'exchanges' && <ExchangeActionsBarSection handleFeedBack={handleFeedBack} />}
			</div>
			<FeedBackTicketModal isOpen={isFeedBackModalOpen} onClose={() => setIsFeedBackModalOpen(false)} type={typeFeedBack} />
			<CloseTicketModal isOpen={isAwaitingClosureTicketModalOpen} onClose={() => setIsAwaitingClosureTicketModalOpen(false)} />
		</>
	);
}
