// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { formatDate, Typography } from '@tools/reactore';
import { isUndefined } from 'lodash';
// api
import { fiberQueryKeys, magnetQueryKeys } from '@/api/queries';
// hooks
import { useRouting } from '@/hooks';
// sections
import AppointmentStateTag from '@/sections/_components/appointment-state-tag';
// components
import { ChevronRightIcon } from '@/components-old/ui/icon';

// ----------------------------------------------------------------------

export default function AppointmentSection() {
	const {
		query: { id },
		isReady
	} = useRouting();

	const { data: ticket } = useQuery({
		...magnetQueryKeys.magnet_ticket.byId(id),
		enabled: Bo<PERSON>an(id && isReady)
	});
	const { data: order } = useQuery({
		...fiberQueryKeys.fiber_order.byId(ticket?.reference),
		enabled: <PERSON><PERSON><PERSON>(ticket)
	});
	const lastAppointment = order?.appointments[0];

	const { state, appointmentRequests, comment } = lastAppointment || {};

	/** Request, which has a schedule with a report */
	const request = appointmentRequests?.find((appointmentRequestItem) => {
		const appointmentSchedule = appointmentRequestItem?.appointmentSchedules?.find((appointmentSchedulesItem) => {
			return !isUndefined(appointmentSchedulesItem.appointmentReport);
		});

		return !isUndefined(appointmentSchedule);
	});
	/** Schedule, which has a report */
	const schedule = request?.appointmentSchedules?.find((appointmentSchedulesItem) => {
		return !isUndefined(appointmentSchedulesItem.appointmentReport);
	});
	const report = schedule?.appointmentReport;

	// const schedule = lastAppointment?.appointmentSchedules?.find((appointmentSchedulesItem) =>
	// 	isUndefined(appointmentSchedulesItem.deletedAt)
	// )

	return (
		<>
			{lastAppointment && (
				<div>
					<div className='mb-1 flex items-center'>
						<Typography className='font-semibold'>Dernier RDV</Typography>
						{schedule?.scheduledAt && (
							<Typography className='ml-4 text-sm' color='gray'>
								{formatDate(schedule.scheduledAt, 'dd/MM/yyyy HH:mm')}
							</Typography>
						)}
					</div>
					<AppointmentStateTag state={state} report={report} />
					<div className='my-2 rounded-lg bg-white px-2.5 py-2 text-xs text-free-gray-500'>
						{comment}
						<button className='ml-auto mt-2 flex w-fit items-center justify-end text-xs font-semibold text-blue-500'>
							Voir le PV
							<ChevronRightIcon className='size-4' />
						</button>
					</div>
				</div>
			)}
		</>
	);
}
