// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { Button, capitalizeWords, Typography } from '@tools/reactore';
import { isUndefined } from 'lodash';
// api
import { fiberQueryKeys, magnetQueryKeys } from '@/api/queries';
// hooks
import { useRouting } from '@/hooks';
// sections
import AppointmentSection from './appointment-section';
import VlanState from './vlan-state';
import RetailerIcon from '@/sections/_components/retailer-icon';
import { ReferenceFormater } from '@/sections/_components';
// components
import { ScanEyeIcon } from '@/components-old/ui/icon';
// others
import OrderMap from '../order-map';

// ----------------------------------------------------------------------

export default function TicketOrder() {
	const {
		query: { id },
		isReady,
		push
	} = useRouting();

	const { data: ticket } = useQuery({
		...magnetQueryKeys.magnet_ticket.byId(id),
		enabled: Boolean(id && isReady)
	});
	const { data: order } = useQuery({
		...fiberQueryKeys.fiber_order.byId(ticket?.reference),
		enabled: Boolean(ticket)
	});

	const { company, endpointAddress, currentNetworks } = order || {};

	return (
		<>
			<div className='flex justify-between mb-1'>
				<div className='flex items-center'>
					{typeof order?.retailer !== 'string' && order?.retailer?.name && <RetailerIcon name={order.retailer.name} />}
					<ReferenceFormater className='text-xl' str={order?.reference || 'N/A'} />
				</div>
				<Button
					disabled={isUndefined(order?.['@id'])}
					onClick={() => {
						if (order) push(`/fiber${order['@id']}`);
					}}>
					<ScanEyeIcon className='size-7 text-free-gray-500' />
				</Button>
			</div>
			<Typography className='text-xs' color='gray'>
				{order &&
					capitalizeWords(
						`${order?.endpointAddress?.street}, ${order?.endpointAddress?.postalCode} ${order?.endpointAddress?.city}`
					)}
			</Typography>
			<VlanState />
			<OrderMap reference={ticket?.reference} />
			<div className='flex flex-col mt-10 space-y-10'>
				<div>
					<Typography className='font-semibold'>Commande</Typography>
					<ul className='mt-1'>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>Bâtiment</Typography>
							<Typography className='basis-1/2'>{order?.currentOis?.buildingName}</Typography>
						</li>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>Escalier</Typography>
							<Typography className='basis-1/2'>{order?.currentOis?.stairCaseName}</Typography>
						</li>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>Étage</Typography>
							<Typography className='basis-1/2'>{order?.currentOis?.floorName}</Typography>
						</li>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>PM</Typography>
							<Typography className='basis-1/2'>{order?.currentOis?.pmName || 'N/A'}</Typography>
						</li>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>PTO</Typography>
							<Typography className='basis-1/2'>{order?.currentOis?.ptoName || 'N/A'}</Typography>
						</li>
					</ul>
				</div>
				<div>
					<Typography className='font-semibold'>Identifiants</Typography>
					<ul className='mt-1'>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>Retailer ID</Typography>
							<Typography className='basis-1/2'>
								{typeof order?.retailer !== 'string' && order?.retailer?.reference ? order.retailer.reference : 'N/A'}
							</Typography>
						</li>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>Client ID</Typography>
							<Typography className='basis-1/2'>{company?.name || 'N/A'}</Typography>
						</li>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>Flux ID</Typography>
							<Typography className='basis-1/2'>N/A</Typography>
						</li>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>Adresse ID</Typography>
							<Typography className='basis-1/2'>{endpointAddress?.iwId || 'N/A'}</Typography>
						</li>
						<li className='flex text-sm'>
							<Typography className='basis-1/2 text-free-gray-500'>Presta ID</Typography>
							<Typography className='basis-1/2'>{currentNetworks?.prestaId || 'N/A'}</Typography>
						</li>
					</ul>
				</div>
				<AppointmentSection />
			</div>
		</>
	);
}
