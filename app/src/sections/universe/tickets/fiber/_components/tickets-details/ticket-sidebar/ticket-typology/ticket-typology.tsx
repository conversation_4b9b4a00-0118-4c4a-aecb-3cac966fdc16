import { useState } from 'react';
// tanstack
import { useQuery } from '@tanstack/react-query';
// utils
import { getGenericEventState } from '@/utils/common.utils';
import { pluralize } from '@/utils/string.utils';
import { Typography } from '@tools/reactore';
import { snakeCase } from 'lodash';
// api
import { magnetQueryKeys } from '@/api/queries';
// hooks
import { useRouting } from '@/hooks';
import { useOrderById } from '@/hooks/queries/use-order-by-id';
// others
import CablesButton from '../line-typology/cables-button';
import TypologyItem from '../line-typology/typology-item';

// ----------------------------------------------------------------------

export default function TicketTypology() {
	const {
		query: { id },
		isReady
	} = useRouting();

	const { data: ticket } = useQuery({
		...magnetQueryKeys.magnet_ticket.byId(id),
		enabled: Boolean(id && isReady)
	});
	const { order, vlan, linkState, appointment, network, oi, rop, offerOrder, onu } = useOrderById(ticket?.reference as string);

	const [tab, setTab] = useState<string | null>(null);

	const handleClick = (id: string) => {
		if (id === tab) {
			setTab(null);
			return;
		}
		setTab(id);
	};

	const pmToNroItemNetworks = rop?.networkComponents?.filter((item) => ['CAD', 'CTR', 'CDI'].includes(item.type));
	const ptoToPmItemNetworks = rop?.networkComponents?.filter((item) => ['PBO', 'FIBRE_CLIENT', 'FIBRE_PM'].includes(item.type));

	return (
		<div>
			<Typography className='text-xl font-semibold'>Typologie de la ligne</Typography>
			<div className='flex flex-col items-center my-6 space-y-4'>
				{vlan?.['@type'] && (
					<TypologyItem
						id='collecte'
						description='Collecte'
						tab={tab}
						state={getGenericEventState(pluralize(vlan['@type']), vlan.state)}
						value={vlan.provider?.toUpperCase()}
						handleClick={handleClick}
					/>
				)}
				{network?.['@type'] && network.state && (
					<>
						<div className='h-5 border' />
						<TypologyItem
							id='nro'
							description='NRO'
							tab={tab}
							state={getGenericEventState(pluralize(network['@type']), network.state)}
							value={rop?.nro?.name}
							handleClick={handleClick}
						/>
					</>
				)}
				{Boolean(pmToNroItemNetworks?.length) && (
					<>
						<div className='h-5 border' />
						<CablesButton
							id='cablesPmToNro'
							tab={tab}
							value={pmToNroItemNetworks?.length ? pmToNroItemNetworks.length.toString() : '0'}
							handleClick={handleClick}
						/>
					</>
				)}
				{oi && oi['@type'] && oi.state && (
					<>
						<div className='h-5 border' />
						<TypologyItem
							id='pm'
							description='PM'
							tab={tab}
							state={getGenericEventState(pluralize(oi['@type']), oi.state)}
							value={rop?.pm?.name}
							handleClick={handleClick}
						/>
					</>
				)}

				{Boolean(ptoToPmItemNetworks?.length) && (
					<>
						<div className='h-5 border' />
						<CablesButton
							id='cablesPtoToPm'
							tab={tab}
							value={ptoToPmItemNetworks?.length ? ptoToPmItemNetworks.length.toString() : '0'}
							handleClick={handleClick}
						/>
					</>
				)}
				{appointment && appointment['@type'] && appointment.state && (
					<>
						<div className='h-5 border' />
						<TypologyItem
							id='pto'
							description='PTO'
							tab={tab}
							state={getGenericEventState(pluralize(appointment?.['@type']), appointment?.state)}
							value={rop?.pto?.name}
							handleClick={handleClick}
						/>
					</>
				)}
				{linkState && linkState['@type'] && linkState.state && (
					<>
						<div className='h-5 border' />
						<TypologyItem
							id='onu'
							tab={tab}
							description='ONU'
							state={getGenericEventState(pluralize(snakeCase(linkState['@type'])), linkState.state)}
							value={
								order?.logisticOrderItems.find((item) => item.onus?.find((onuItem) => onuItem['@id'] === onu?.['@id']))
									?.serial1
							}
							handleClick={handleClick}
						/>
					</>
				)}
				{linkState &&
					linkState['@type'] &&
					linkState.state &&
					order?.offerOrders.find((offerOrder) => offerOrder.offer.reference === 'box1') && (
						<>
							<div className='h-5 border' />
							<TypologyItem
								id='box'
								tab={tab}
								description='Box'
								state={getGenericEventState(pluralize(snakeCase(linkState['@type'])), linkState.state)}
								value={offerOrder?.offer.name}
								handleClick={handleClick}
							/>
						</>
					)}
			</div>
		</div>
	);
}
