// utils
import type { TFetcherParamsMethods } from '@tools/reactore';
import { number, object, string, type z } from 'zod';
// api
import type { entityJsonLdSchema } from '@/api/interface';

// ----------------------------------------------------------------------
// services

// ----------------------------------------------------------------------

/* *---------------------------------------------------------------------------* */
/* *------------------------------ Old to review ------------------------------* */

export type TMethodsFetcher = 'get' | 'put' | 'delete' | 'post' | 'patch';
export type TApiArg = TFetcherParamsMethods & {
	method?: TMethodsFetcher;
};
/* *---------------------------------------------------------------------------* */
/* *---------------------------------------------------------------------------* */

/* *---------------------------------------------------------------------------* */
/* *--------------------------------- Params ---------------------------------* */
export const paginationParamsSchema = object({
	page: number(),
	itemsPerPage: number()
}).partial();
export const createdAtParamsSchema = object({
	'createdAt[before]': string(),
	'createdAt[strictly_before]': string(),
	'createdAt[after]': string(),
	'createdAt[strictly_after]': string()
}).partial();

/* *---------------------------------------------------------------------------* */
/* *---------------------------------------------------------------------------* */
export type TEntityJsonLd = z.infer<typeof entityJsonLdSchema>;
export type TPaginationParams = z.infer<typeof paginationParamsSchema>;
export type TCreatedAtParams = z.infer<typeof createdAtParamsSchema>;
