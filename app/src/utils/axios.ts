// utils
import type { AxiosRequestConfig } from 'axios';
import axios from 'axios';
// config
import { METEOR_API, PAWN_API } from '@/global-config';
// contexts
import { getSession } from '@/auth/utils';

// ----------------------------------------------------------------------

// ================================
// AXIOS PAWN API PLATFORM INSTANCE
// ================================
const axiosPawnApiPlatformInstance = axios.create({ baseURL: PAWN_API });

axiosPawnApiPlatformInstance.interceptors.request.use(
	(config) => {
		const accessToken = getSession();

		if (accessToken) {
			config.headers.Authorization = `Bearer ${accessToken}`;
		}

		return config;
	},
	(error) => Promise.reject(error)
);

axiosPawnApiPlatformInstance.interceptors.response.use(
	async (response) => {
		if (response.config.method === 'get') {
			let formattedData = {};

			if (response.status === 200) {
				formattedData = {
					totalItems: response.data['hydra:totalItems'],
					items: response.data['hydra:member']
				};
			} else {
				formattedData = response.data;
			}

			return { ...response, data: formattedData };
		}

		if (response.config.method === 'post' || response.config.method === 'put' || response.config.method === 'delete') {
			let formattedData = {};

			// check response is a success
			if (response.status === 200 || response.status === 201 || response.status === 204) {
				formattedData = {
					totalItems: response.data['hydra:totalItems'],
					items: response.data['hydra:member']
				};
			} else {
				return Promise.reject((response && response.data) || 'Something went wrong');
			}

			return { ...response, data: formattedData };
		}

		return response;
	},
	async (error) => {
		console.error(error);
	}
);

// =======================
// AXIOS METEOR INSTANCE
// =======================
const axiosInstance = axios.create({ baseURL: METEOR_API });

// axiosInstance.defaults.headers.common.Accept = 'application/json';

axiosInstance.interceptors.request.use(
	(config) => {
		const accessToken = getSession();

		if (accessToken) {
			config.headers.Authorization = `Bearer ${accessToken}`;
		}

		return config;
	},
	(error) => Promise.reject(error)
);

export { axiosInstance };
export default axiosInstance;

// ===================================
// AXIOS METEOR API PLATFORM INSTANCE
// ===================================
const axiosApiPlatformInstance = axios.create({ baseURL: METEOR_API });

axiosApiPlatformInstance.interceptors.request.use(
	(config) => {
		const accessToken = getSession();

		if (accessToken) {
			config.headers.Authorization = `Bearer ${accessToken}`;
		}

		return config;
	},
	(error) => Promise.reject(error)
);

axiosApiPlatformInstance.interceptors.response.use(
	async (response) => {
		if (response.config.method === 'get') {
			let formattedData = {};

			if (response.status === 200) {
				formattedData = {
					totalItems: response.data['hydra:totalItems'],
					items: response.data['hydra:member']
				};
			} else {
				formattedData = response.data;
			}

			return { ...response, data: formattedData };
		}

		if (response.config.method === 'post' || response.config.method === 'put' || response.config.method === 'delete') {
			let formattedData = {};

			// check response is a success
			if (response.status === 200 || response.status === 201 || response.status === 204) {
				formattedData = {
					totalItems: response.data['hydra:totalItems'],
					items: response.data['hydra:member']
				};
			} else {
				return Promise.reject((response && response.data) || 'Something went wrong');
			}

			return { ...response, data: formattedData };
		}

		return response;
	},
	async (error) => {
		console.error(error);
	}
);

// ----------------------------------------------------------------------

export const fetcher = async (args: string | [string, AxiosRequestConfig]) => {
	const [url, config] = Array.isArray(args) ? args : [args];

	const res = await axiosInstance.get(url, { ...config });

	return res.data;
};

// ----------------------------------------------------------------------

export const API_ENDPOINTS_CELL = {
	customer_sim_card: {
		root: '/customer_sim_cards'
	},

	retailers: {
		root: '/retailers',
		is_active: '/retailers/is_active'
	},

	stats: {
		root: '/stats'
	}
};

export const API_ENDPOINTS_DELIVER = {
	catalog: {
		root: '/catalog'
	},
	product_supply: {
		root: '/product_supplies'
	},
	product_payment_strategy: {
		root: '/product_payment_strategies'
	},
	order: {
		root: '/orders',
		editItems: '/orders/items',
		retailers: '/retailers/orders',
		staff: '/staff/orders'
	},
	mondial_relay: {
		root: '/relay_points'
	},
	delivery_address: {
		root: '/delivery_addresses'
	},
	post_delivery: {
		root: '/post_deliveries'
	},
	enum: {
		orderState: '/order_state_enums'
	}
};

export const API_ENDPOINTS_METEOR = {
	directories: {
		shop: '/directories/shop',
		shop_contacts: '/directories/shop_contacts',
		marketing_mall_contacts: '/directories/marketing_mall_contacts',
		security_agent_contacts: '/directories/security_agent_contacts',
		technical_contacts: '/directories/technical_contacts'
	},
	places: {
		area: '/places/area',
		areas: '/places/areas',
		areaManager: '/places/area_manager',
		region: '/places/region',
		regions: '/places/regions',
		shops: '/places/shops',
		shop_addresses: '/places/shop_addresses',
		commercial: '/places/commercial'
	},
	planning: {
		root: '/plannings',
		commercial: '/commercial'
	},
	postal_codes: '/postal_codes',
	reviews: {
		root: '/reviews',
		currentVisit: '/commercial/reviews/visit/current',
		currentPictures: '/reviews/current/pictures',
		currentComplement: '/commercial/reviews/complement/current',
		shop: '/shops'
	}
};

export const API_ENDPOINTS_PAWN = {
	me: `/me`,

	staff: {
		// USERS
		root: '/staff',

		// USER
		login: '/staff/login',
		register: '/staff/register',
		refreshToken: '/staff/refresh_token',
		resetPassword: '/staff/reset_password',
		logout: '/staff/logout'
	},

	// SPACES
	spaces: '/spaces',

	// DOMAINS
	domains: '/domains',

	// TEAM
	teams: '/teams',

	// JOB
	jobs: '/jobs',

	// GOALS
	goals: '/goals',

	// RETAILER_CONTACTS
	retailer_contacts: '/retailer_contacts',

	// RETAILER
	retailer: {
		root: '/retailers'
	},

	// DOCUMENTS
	documents: '/documents'
};

export const API_ENDPOINTS_PIGGY = {
	billing_address: {
		root: '/billing_addresses'
	},
	enum: {
		country: '/country_enums'
	}
};

export const API_ENDPOINTS_MAGNET = {
	ticket: {
		root: '/tickets'
	}
};

export const API_ENDPOINTS_FIBER = {
	order: {
		root: '/orders'
	}
};

export { axiosApiPlatformInstance };
