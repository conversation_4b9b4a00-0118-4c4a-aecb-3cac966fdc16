// ----------------------------------------------------------------------

// ex: capitalizeCase('hello') => 'Hello'
export function capitalizeCase(str: string) {
	return str.charAt(0).toUpperCase() + str.slice(1);
}

// ex: capitalizeSentCase('hello world') => 'Hello world'
export function capitalizeSentCase(str: string) {
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// ex: fullNameCase('hello', 'world') => 'Hello WORLD'
export function fullNameCase(firstName?: string, lastName?: string) {
	if (!firstName || !lastName) return 'N/A';

	return `${firstName.charAt(0).toUpperCase() + firstName.slice(1)} ${lastName.toUpperCase()}`;
}

// ex: snakeCase('hello world') => 'hello_world'
export function snakeCase(str: string) {
	return str
		.toLowerCase()
		.replace(/\s+/g, '_')
		.replace(/[^a-z0-9_]/g, '');
}

// ex: titleCase('hello world') => 'Hello World'
export function titleCase(str: string) {
	return str
		.toLowerCase()
		.replace(/\s+/g, ' ')
		.replace(/[^a-z0-9 ]/g, '')
		.replace(/^(.)|\s+(.)/g, (match) => match.toUpperCase());
}

// ex: pascalCase('hello world') => 'HelloWorld'
export function pascalCase(str: string) {
	return str
		.toLowerCase()
		.replace(/\s+/g, '-')
		.replace(/[^a-z0-9-]/g, '')
		.replace(/-(.)/g, (_match, group1) => group1.toUpperCase())
		.replace(/^(.)/, (_match, group1) => group1.toUpperCase());
}

// ex: constantCase('hello world') => 'HELLO_WORLD'
export function constantCase(str: string) {
	return str
		.toLowerCase()
		.toUpperCase()
		.replace(/\s+/g, '_')
		.replace(/[^a-z0-9_]/g, '');
}

// ex: dotCase('hello world') => 'hello.world'
export function dotCase(str: string) {
	return str
		.toLowerCase()
		.replace(/\s+/g, '.')
		.replace(/[^a-z0-9.]/g, '');
}

// ex: headerCase('hello world') => 'Hello-World'
export function headerCase(str: string) {
	return str
		.toLowerCase()
		.replace(/\s+/g, '-')
		.replace(/[^a-z0-9-]/g, '')
		.replace(/-(.)/g, (_match, group1) => group1.toUpperCase());
}

// ex: pathCase('hello world') => 'hello/world'
export function pathCase(str: string) {
	return str
		.toLowerCase()
		.replace(/\s+/g, '/')
		.replace(/[^a-z0-9/]/g, '');
}

// ex: lowerCase('hello world') => 'hello world'
export function lowerCase(str: string) {
	return str.toLowerCase();
}

// ex: upperCase('hello world') => 'HELLO WORLD'
export function upperCase(str: string) {
	return str.toUpperCase();
}

// ex: sentenceCase('hello world') => 'Hello world'
export function sentenceCase(str: string) {
	// eslint-disable-next-line no-useless-escape
	return str.toLowerCase().replace(/(^\s*\w|[\.\!\?]\s*\w)/g, (c) => c.toUpperCase());
}

// ex: swapCase('hello world') => 'HELLO WORLD'
export function swapCase(str: string) {
	return str.replace(/\S/g, (match) => {
		const lower = match.toLowerCase();
		return lower === match ? match.toUpperCase() : lower;
	});
}
