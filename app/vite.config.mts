/// <reference types="vite/client" />

import { defineConfig, loadEnv } from 'vite';
// plugins
import { sentryVitePlugin } from '@sentry/vite-plugin';
import { TanStackRouterVite } from '@tanstack/router-plugin/vite';
import react from '@vitejs/plugin-react-swc';
import path from 'node:path';
import checker from 'vite-plugin-checker';
import tsconfigPaths from 'vite-tsconfig-paths';
import svgr from 'vite-plugin-svgr';

import { version } from './package.json';

// -------------------------------------------------------

export default defineConfig(({ mode }) => {
	const envSentry = loadEnv(mode, process.cwd(), 'SENTRY_');
	const envProject = loadEnv(mode, process.cwd(), 'PROJECT_');
	const envVite = loadEnv(mode, process.cwd());
	const env = loadEnv(mode, process.cwd(), '');

	const port = parseInt(envVite['VITE_PORT'], 10);
	const host = envVite['VITE_HOST'] ? envProject['PROJECT_HOST'] : undefined;
	const https = envVite['VITE_HTTPS']
		? {
				cert: './certs/localhost+3.pem',
				key: './certs/localhost+3-key.pem'
			}
		: undefined;

	return {
		base: '/',
		build: {
			sourcemap: true,
			rollupOptions: {
				output: {
					manualChunks: {
						vendor: ['react', 'react-dom'],
						lodash: ['lodash']
					},
					sourcemapPathTransform: (relativeSourcePath) => {
						return relativeSourcePath.replace(/^(\.\.\/)+/, '');
					}
				}
			}
		},

		cacheDir: './.vite-cache',

		css: {
			preprocessorOptions: {
				scss: {
					api: 'modern'
				}
			}
		},

		define: {
			'import.meta.env.NODE_ENV': JSON.stringify(env.NODE_ENV),
			'import.meta.env.APP_ENV': JSON.stringify(env.APP_ENV),
			'import.meta.env.PROJECT_NAME': JSON.stringify(env.PROJECT_NAME),
			'import.meta.env.PROJECT_BUILDER_DATE': JSON.stringify(env.PROJECT_BUILDER_DATE),
			'import.meta.env.PROJECT_BUILDER_VERSION': JSON.stringify(env.PROJECT_BUILDER_VERSION)
		},

		plugins: [
			react(),

			TanStackRouterVite(),

			mode === 'development' &&
				checker({
					root: './src',
					overlay: {
						initialIsOpen: false,
						position: 'bl',
						badgeStyle: 'tr',
						panelStyle: 'opacity: 0.9'
					},
					terminal: true,
					biome: {
						command: 'check'
					},
					typescript: true
				}),

			svgr(),

			tsconfigPaths(),

			sentryVitePlugin({
				authToken: envSentry['SENTRY_AUTH_TOKEN'],
				org: envSentry['SENTRY_ORG'],
				project: envSentry['SENTRY_PROJECT'],
				url: envSentry['SENTRY_URL'],
				debug: true,
				disable: envSentry['SENTRY_STATE'] === 'disabled',
				telemetry: false,
				reactComponentAnnotation: { enabled: true },
				sourcemaps: {
					filesToDeleteAfterUpload: ['**/*.map.js'],
					ignore: ['node_modules']
				},
				release: {
					name: envProject['PROJECT_BUILDER_VERSION'] || `${version}@local`,
					inject: true,
					uploadLegacySourcemaps: {
						paths: ['./out'],
						sourceMapReference: true
					}
				}
			})
		],

		resolve: {
			alias: {
				'@': path.resolve(__dirname, './src'),

				// fix loading all icon chunks in dev mode
				// https://github.com/tabler/tabler-icons/issues/1233
				'@tabler/icons-react': '@tabler/icons-react/dist/esm/icons/index.mjs'
			}
		},

		server: {
			https,
			host,
			port: port || 3000,
			strictPort: true,
			watch: {
				usePolling: true,
				interval: 100
			}
		},

		preview: {
			https,
			host,
			port: port || 8081,
			strictPort: true
		}
	};
});
