# APP BUILD CONFIGURATION FILE

group "base" {
  targets = ["app"]
}

###########
# Environnements
# Note: Dont use APP_ENV here.
###########

// The name used to push tags. Should reflect the Gitlab relative path
variable "APP_NAME" { default = "front/proui" }

// false: Use default build steps / use default conf files
// true: Use compilation build stets / use compiled conf files
variable "WITH_COMPILATION" { default = false }

// true: Uses docker compose or swarm to deploy / With debugging layers / downloads packages on start
// false: Uses swarm or kube to deploy / No debugging layers / fully compiled (no download on start)
variable "WITH_DEBUGGING" { default = true }

// The tag used to push to the registry
// Use "local" if no registry is used
variable "IMG_TAG" { default = "local" }

// Set where and when the image was built (should be set by appx)
variable "BUILDER_ORIGIN" { default = "unavailable" }
variable "BUILDER_DATE" { default = "unavailable" }
variable "BUILDER_VERSION" { default = "0.0.0" }

variable "SENTRY_ORG" { default = "unavailable" }
variable "SENTRY_PROJECT" { default = "proui" }
variable "SENTRY_URL" { default = "unavailable" }
variable "SENTRY_AUTH_TOKEN" { default = "unavailable" }
variable "SENTRY_STATE" { default = "disabled" }

###########
# Base config layer we will extends
###########

target "_" {
  dockerfile = "deploy/Dockerfile"
  labels = {
    "app.proteam.builder.origin" = BUILDER_ORIGIN
    "app.proteam.builder.version" = BUILDER_VERSION
    "app.proteam.builder.with_compilation" = WITH_COMPILATION
    "app.proteam.builder.with_debugging" = WITH_DEBUGGING
  }
  args = {
    NODE_VERSION = "${NODE_VERSION}"
    NGINX_VERSION = "${NGINX_VERSION}"
    WITH_COMPILATION = "${WITH_COMPILATION}"
    WITH_DEBUGGING = "${WITH_DEBUGGING}"
    BUILDER_DATE = "${BUILDER_DATE}"
    BUILDER_VERSION = "${BUILDER_VERSION}"
    GL_TOKEN = "${GL_TOKEN}"
    SENTRY_STATE = "${SENTRY_STATE}"
    SENTRY_AUTH_TOKEN = "${SENTRY_AUTH_TOKEN}"
    SENTRY_ORG = "${SENTRY_ORG}"
    SENTRY_PROJECT = "${SENTRY_PROJECT}"
    SENTRY_URL = "${SENTRY_URL}"
  }
}

###########
# Building all layers
###########

target "node" {
  inherits = ["_"]
  target = "node-base"
}

target "node-builder" {
  inherits = ["_"]
  target = "node-builder"
}

target "nginx" {
  inherits = ["_"]
  target = "nginx"
  contexts = {
    "node-builder" = "target:node-builder"
  }
}

target "app" {
  inherits = ["_"]
  target = "end-layer"
  tags = ["${APP_NAME}/app:${IMG_TAG}"]
  contexts = {
    "layer" = WITH_COMPILATION ? "target:nginx" : "target:node"
  }
}
