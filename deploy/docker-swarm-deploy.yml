version: "3.8"

# You should not use this file in local
# Used by GitLab CI to deploy in dev or prod servers

networks:
  traefik-public:
    external: true

x-env: &environment
  APP_ENV: ${APP_ENV:-undefined_var}
  DEPLOY_TYPE: swarm

x-baseconfig: &baseconfig
  mode: replicated
  replicas: 1
  restart_policy:
    condition: any
    delay: 120s
    max_attempts: 2
  update_config:
    parallelism: 1
    delay: 10s
    order: start-first
    failure_action: pause
    monitor: 30s
    max_failure_ratio: 0
  placement:
    max_replicas_per_node: 1

services:
  ## main application web server
  ## -------------------------------
  app:
    image: ${APP_NAME}/app:${IMG_TAG}
    deploy:
      <<: *baseconfig
      resources:
        reservations:
          memory: 256M
          cpus: "0.5"
        limits:
          memory: 2G
      endpoint_mode: dnsrr
      labels:
        - traefik.enable=true
        - traefik.docker.network=traefik-public
        - traefik.constraint-label=traefik-public
        - traefik.http.routers.${SERVICE_NAME}.service=${SERVICE_NAME}
        - traefik.http.routers.${SERVICE_NAME}.rule=Host(`${DEPLOY_URL}`)
        - traefik.http.routers.${SERVICE_NAME}.entrypoints=internal
#        - traefik.http.routers.${SERVICE_NAME}.middlewares=${SERVICE_NAME}
#        - traefik.http.middlewares.${SERVICE_NAME}.basicauth.removeheader=true
#        - traefik.http.middlewares.${SERVICE_NAME}.basicauth.users=prepaid:$$2y$$05$$SXfAQxV42CpHPDT0JfPQk.kTOwtVNuHFmL71zZWcdGZzIqN/lHjVm
        - traefik.http.services.${SERVICE_NAME}.loadbalancer.server.port=80
    networks:
      - traefik-public
    environment:
      <<: *environment
      DEPLOY_SERVER: ${DEPLOY_SERVER}
      DEPLOY_URL: ${DEPLOY_URL}
      PUBLIC_PAWN_API_URL: ${PUBLIC_PAWN_API_URL}
      PUBLIC_MAGNET_API_URL: ${PUBLIC_MAGNET_API_URL}
      PUBLIC_CELL_API_URL: ${PUBLIC_CELL_API_URL}
      PUBLIC_FIBER_API_URL: ${PUBLIC_FIBER_API_URL}
      SENTRY_STATE: ${SENTRY_STATE}
